<template>
  <div class="main-content">
    <component
      :is="comModule"
      :target-data="targetData"
      :btn-authority-list="btnAuthorityList"
      :is-show-battery-manage="isShowBatteryManage"
      :is-show-dts-manage="isShowDtsManage"
      @changePage="changePage"
    />
  </div>
</template>

<script>
import { deepClone } from '@/utils'
import { mapGetters } from 'vuex'
import firstPage from './modules/firstPage'
import batteryCabManage from './modules/batteryCabManage'
import pointManage from './modules/pointManage'
import newDTS from './modules/newDTS'
import opticalFiberManage from './modules/opticalFiberManage'
import newopticalFiber from './modules/newopticalFiber'
import locate from './modules/locate'

export default {
  name: 'DeviceManage',
  components: {
    firstPage,
    batteryCabManage,
    pointManage,
    newDTS,
    opticalFiberManage,
    newopticalFiber,
    locate
  },
  data() {
    return {
      menuList: JSON.parse(localStorage.getItem('menuList')),
      btnAuthorityList: {
        batteryManageBtn: [], // 煤矿管理按钮权限
        DTSManageBtn: [] // DTS管理按钮权限
      }, // 按钮权限
      isShowBatteryManage: false, // 煤矿管理菜单权限
      isShowDtsManage: false, // DTS管理菜单权限
      targetData: {},
      comModule: 'firstPage'
    }
  },
  computed: {
    ...mapGetters([
      'btnAuthority'
    ])
  },
  created() {
    this.isShowBatteryManage = this.menuList.includes('KJYSBGL-DLXXGL')

    this.isShowDtsManage = this.menuList.includes('KJYSBGL-DTSGL')
    const cur1 = this.btnAuthority.find((item) => item.code === 'KJYSBGL-DLXXGL')
    const cur2 = this.btnAuthority.find((item) => item.code === 'KJYSBGL-DTSGL')
    if (cur1) this.btnAuthorityList.batteryManageBtn = cur1.functionPermissionCode
    if (cur2) this.btnAuthorityList.DTSManageBtn = cur2.functionPermissionCode
    console.log(this.btnAuthorityList, 'btnAuthorityList')
  },
  mounted() {},
  methods: {
    // 切换页面
    changePage(module) {
      this.comModule = module.type
      if (module.data) {
        this.targetData = deepClone(module.data)
      } else {
        this.targetData = {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-content{
  height: calc(100vh - 122px);
  padding: 30px 0;
  box-sizing: border-box;
  overflow: hidden;
}
::v-deep .el-table--medium th {
  padding: 15px 0 !important;
}
::v-deep .el-table--medium td {
  padding: 13px 0 !important;
  font-family: PingFang SC RE;
  font-weight: bold !important;
}
</style>
