<template>
  <div
    v-loading.fullscreen="uploading"
    class="point_manage"
  >
    <div class="top">
      <div class="left">
        <img
          src="@/assets/device_manage/<EMAIL>"
          @click="back"
        >
        <div class="pa_name">空间与设备管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">监控段管理</div>
      </div>
    </div>
    <div style="display:flex;">
      <div
        class="list_box"
      >
        <div>
          <div class="list_name">{{ targetData.name }}</div>
        </div>
        <div
          style="margin-top: 28px;overflow-y: auto;"
          :style="{maxHeight: innerHeight - 330 + 'px'}"
        >
          <el-tree
            ref="tree"
            :data="data"
            :props="treePops"
            :highlight-current="true"
            node-key="id"
            @node-click="handleNodeClick"
          >
            <div
              slot-scope="{ node, data }"
              class="custom-tree-node"
            >
              <span
                :style="{color:node.isCurrent ? '#0358CB' : ''}"
                style="font-weight:bold;"
              >{{ data.name }}</span>
            </div>
          </el-tree>
        </div>

      </div>
      <div style="width: 80%; margin-left: 20px">
        <div class="title">
          <!--          <div class="left">{{ '一号#-煤矿' }}</div>-->
          <div class="right">
            <div>
              <el-input
                v-model="keyword"
                placeholder="请输入搜素内容"
                suffix-icon="el-icon-search"
                style="width:320px;margin-right:20px;"
                @blur="getList(1)"
              />
            </div>
            <div>
              <el-button
                type="primary"
                @click="add"
              >新增
              </el-button>
              <el-button
                type="danger"
                @click="batchDel"
              >批量删除
              </el-button>
              <el-button
                type="primary"
                @click="batchImport"
              >批量导入
              </el-button>
              <el-button
                type="success"
                @click="batchExport"
              >批量导出
              </el-button>
            </div>
          </div>
        </div>
        <div class="table">
          <el-table
            ref="multipleTable"
            v-loading="loading"
            :header-cell-style="tableHeaderStyle"
            header-row-class-name="table-header"
            :data="tableData"
            stripe
            :height="innerHeight - 380"
            row-key="id"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="70"
            />
            <el-table-column
              label="序号"
              type="index"
              width="70"
              align="center"
            />
            <el-table-column
              prop="name"
              label="名称"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.name || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="deviceName"
              label="DTS主机"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.deviceName || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="cableName"
              label="光纤名称"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.cableName || '--' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="创建时间"
              show-overflow-tooltip
              align="center"
            />

            <el-table-column
              label="操作"
              align="center"
              width="160"
            >
              <template v-slot="scope">
                <el-button
                  type="text"
                  style="text-decoration:underline;margin-right:15px;"
                  @click="getDetail(scope.row, '详情')"
                >详情
                </el-button>
                <el-button
                  type="text"
                  style="color:#67C23A;text-decoration:underline;margin-right:15px;"
                  @click="getDetail(scope.row, '编辑')"
                >编辑
                </el-button>
                <el-button
                  type="text"
                  style="color:#F56C6C;text-decoration:underline;"
                  @click="del(scope.row)"
                >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            :current-page.sync="pageNum"
            :page-size="pageSize"
            layout="total,prev, pager, next,sizes, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 导入 -->
      <el-dialog
        title="导入"
        :visible.sync="importDialogVisible"
        width="fit-content"
        :modal-append-to-body="false"
        top="30vh"
        @close="closeImportDialog"
      >
        <div style="padding:0 30px 0;">
          <div style="display:flex;justify-content: space-between;margin-bottom: 30px;align-items: center">
            <div
              slot="tip"
              class="uploadText1"
            >选择文件
              <div class="uploadText2">(支持扩展名:xls、xlsx)</div>
            </div>
            <el-link
              type="primary"
              @click="downloadTemplate"
            >点击此处下载模板</el-link>
          </div>
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            drag
            :on-change="handleImportSubmit"
          >
            <i
              v-show="false"
              class="el-icon-upload"
            />

            <img
              src="@/assets/userManage/<EMAIL>"
              style="width: 25.8px;height: 24.5px;margin: 17% 0 13px;"
            >
            <div class="el-upload__text"><em>选择上传</em></div>
          </el-upload>
        </div>
        <div
          slot="footer"
          class="dialog_footer"
        >
          <!-- <el-button
            type="primary"
            plain
            style="margin-right:10px"
            @click="closeImportDialog()"
          >取 消
          </el-button>
          <el-button
            type="primary"
            @click="handleAlarmSubmit()"
          >确 定
          </el-button> -->
        </div>
      </el-dialog>

    </div>
    <!-- 删除 -->
    <el-dialog
      title="删除"
      :visible.sync="dialogVisibleDel"
      :modal-append-to-body="false"
      width="500px"
      top="320px"
      @close="closeDelDialog()"
    >
      <div style="display:flex;align-items:center;padding-left:50px">
        <img
          src="@/assets/<EMAIL>"
          style="width:20px;height:20px;margin-right:10px"
        >
        <div style="font-size:16px;color:#F94E4E">
          确认删除所选数据吗？
        </div>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button @click="closeDelDialog()">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel()"
        >确 认
        </el-button>
      </div>
    </el-dialog>

    <!-- 新增测温点 -->
    <el-dialog
      :title="titleName"
      :visible.sync="dialogVisible"
      width="950px"
      :modal-append-to-body="false"
      top="320px"
      @close="closeDialog()"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="130px"
        class="form_part"
      >
        <el-form-item
          label="名称:"
          prop="name"
          class="form_item"
        >
          <el-input
            v-model="formData.name"
            placeholder="请输入"
            size="large"
            style="width:320px;"
            :disabled="isCheck"
          />
        </el-form-item>

        <el-form-item
          label="空间序列号:"
          prop="serialNum"
          class="form_item"
        >

          <el-input
            v-model="formData.serialNum"
            placeholder="请输入"
            size="large"
            style="width:320px;"
            @keyup.native="formData.serialNum = numberInput(formData.serialNum)
            "
          />
        </el-form-item>

        <el-form-item
          label="DTS主机:"
          prop="deviceId
"
          class="form_item"
        >
          <el-select
            v-model="formData.deviceId
            "
            placeholder="请选择"
            size="large"
            style="width:320px;"
            :disabled="isCheck"
            @change="dtsChange"
          >
            <el-option
              v-for="item in dtsList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="光纤名称:"
          prop="cableId"
          class="form_item"
        >
          <el-select
            v-model="formData.cableId"
            placeholder="请选择"
            size="large"
            style="width:320px;"
            :disabled="!formData.deviceId
              || isCheck"
            @change="cableIdChange"
          >
            <el-option
              v-for="item in opticalFiberList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="光纤段:"
          prop="paragraphId"
          class="form_item"
        >
          <el-select
            v-model="formData.paragraphId"
            placeholder="请选择"
            size="large"
            style="width:320px;"
            :disabled="!formData.cableId || isCheck"
          >
            <el-option
              v-for="item in distanceList"
              :key="item.id"
              :label="`${item.startPosition / 100}m-${item.endPosition / 100}m`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          class="range-select"
          prop="endPosition"
          label="位置:"
        >
          <el-input-number
            v-model="formData.startPosition"
            :min="0"
            :controls="false"
            style="width: 100px"
          />
          <span style="margin: 0 10px">~</span>
          <el-input-number
            v-model="formData.endPosition"
            :min="formData.startPosition"
            :controls="false"
            style="width: 100px"
          />
          <span style="margin-left: 6px;">米</span>
        </el-form-item>

        <el-form-item
          label="工作面:"
          prop="workFaceId"
          class="form_item"
        >
          <el-select
            v-model="formData.workFaceId"
            style="width:320px;"
            @change="changeWorkFace"
          >
            <el-option
              v-for="item in spaceList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          class="range-select"
          label="历史数据保存位置:"
        >
          <el-input-number
            v-model="formData.historyStartPosition"
            :min="formData.startPosition"
            :max="formData.endPosition"
            :controls="false"
            style="width: 100px"
          />
          <span style="margin: 0 10px">~</span>
          <el-input-number
            v-model="formData.historyEndPosition"
            :min="formData.historyStartPosition"
            :max="formData.endPosition"
            :controls="false"
            style="width: 100px"
          />
          <span style="margin-left: 6px;">米</span>
        </el-form-item>

        <el-form-item
          label="标绘:"
          prop="pointCellType"
          class="form_item"
        >
          <el-button
            type="primary"
            :disabled="!workFace"
            :loading="pointLoading"
            @click="locate"
          >标绘</el-button>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          v-if="!isCheck"
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeDialog()"
        >取 消
        </el-button>
        <el-button
          v-if="isCheck"
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeDialog()"
        >关 闭
        </el-button>
        <el-button
          v-if="!isCheck"
          :loading="fullscreenLoading"
          type="primary"
          @click="handleSubmit()"
        >确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 导入 -->
    <el-dialog
      title="导入"
      :visible.sync="importDialogVisible"
      width="fit-content"
      :modal-append-to-body="false"
      top="30vh"
      @close="closeImportDialog()"
    >
      <div style="padding:0 30px 0;">
        <div style="display:flex;justify-content: space-between;margin-bottom: 30px;align-items: center">
          <div
            slot="tip"
            class="uploadText1"
          >选择文件
            <div class="uploadText2">(支持扩展名:xls、xlsx)</div>
          </div>
          <el-link
            type="primary"
            @click="downloadTemplate"
          >点击此处下载模板</el-link>
        </div>
        <el-upload
          class="upload-demo"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          drag
          :on-change="handleImportSubmit"
        >
          <i
            v-show="false"
            class="el-icon-upload"
          />

          <img
            src="@/assets/userManage/<EMAIL>"
            style="width: 25.8px;height: 24.5px;margin: 17% 0 13px;"
          >
          <div class="el-upload__text"><em>选择上传</em></div>
        </el-upload>
      </div>
    </el-dialog>

    <el-dialog
      :title="resultData.successTotal ? '导入成功' : '导入失败'"
      :visible.sync="dialogVisibleImport"
      width="480px"
      :before-close="handleCloseImport"
      :modal-append-to-body="false"
    >
      <import-result
        :result-data="resultData"
        @handleClose="handleCloseImport"
        @submitForm="submitFormImport"
        @downFail="downFail"
      />
    </el-dialog>
    <!--    标绘弹窗-->
    <el-dialog
      :visible.sync="locateDialogVisible"
      width="fit-content"
      title="标绘"
      top="5vh"
      modal-append-to-body
    >
      <LocateDialog
        v-if="locateDialogVisible"
        ref="locateRef"
        :image="workFace.image"
        :points="formData.points"
        :segments="segments"
        :ratio="workFace.ratio"
      />
      <template #footer>
        <el-button @click="locateDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="changePoint"
        >确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone, utf8to16 } from '@/utils'
import { mapGetters } from 'vuex'
import {
  pointPage,
  paragraphDropdown,
  addPoint,
  updatePoint,
  pointExport,
  getPointTree,
  opticalFiberDropdown,
  downloadPointTemplate,
  importPoint, workFaceList, delPoint, pointList
} from '@/api/deviceManage'
import { deviceDropdown } from '@/api/lineCharts'
import importResult from '@/components/importResult'
import VirtualSelect from '@/components/VirtualSelect/Select'
import LocateDialog from '@/components/LocateDialog/LocateDialog.vue'

export default {
  name: 'BatteryCabManage',
  components: {
    LocateDialog, importResult, VirtualSelect
  },
  props: {
    targetData: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      parentSerialNum: null,
      treePops: {
        label: 'name',
        children: 'childList'
      },
      data: [{
        name: '全部',
        spaceType: 0,
        id: -1,
        serialNum: null,
        childList: []
      }],
      keyword: '',
      loading: false,
      tableData: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      selectList: [], // 选中数据
      select: {},

      dtsList: [], // DTS主机
      opticalFiberList: [], // 光纤名称
      distanceList: [], // 监控段
      areaList: [], // 位置信息
      spaceList: [], // 工作面
      formData: {
        name: '', // 名称
        deviceId: '', // DTS主机
        cableId: '', // 光纤名称
        paragraphId: '', // 光纤段
        batteryNum: '', // 电池编号
        workFaceId: '', // 工作面
        startPosition: null,
        endPosition: null
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        deviceId: [
          { required: true, message: '请选择DTS主机', trigger: 'change' }
        ],
        cableId: [
          { required: true, message: '请选择光纤名称', trigger: 'change' }
        ],
        paragraphId: [
          { required: true, message: '请选择光纤段', trigger: 'change' }
        ],
        workFaceId: [{ required: true, message: '请选择工作面', trigger: 'change' }],
        endPosition: [{ required: true, message: '请输入位置信息', trigger: 'change' }],
        serialNum: [{ required: true, message: '请输入空间序列号', trigger: 'change' }]
      },
      dialogVisible: false,
      dialogVisibleDel: false,
      titleName: '新增',
      isCheck: false,
      importDialogVisible: false,
      resultData: {}, // 导入结果
      dialogVisibleImport: false,
      innerHeight: null,
      uploading: false, // 导入状态
      areaLoading: false,
      locateDialogVisible: false,
      workFace: null,
      points: [],
      segments: [],
      pointLoading: false,
      fullscreenLoading: false
    }
  },
  computed: {
    ...mapGetters(['tableHeaderStyle'])
  },
  mounted() {
    this.innerHeight = window.innerHeight
    window.onresize = () => {
      this.innerHeight = window.innerHeight
    }
    this.getList(1)
    this.deviceDropdown()
    this.spaceCascadeDropdown()
    this.getPointTree()
    this.parentSerialNum = `1${this.targetData.serialNum}`
    this.data[0].serialNum = `1${this.targetData.serialNum}`
  },
  methods: {
    // 设置树名称
    setTreeName(arr) {
      arr.forEach((item) => {
        if (item.spaceName && !item.name) {
          this.$set(item, 'name', item.spaceName)
        }
        if (item.childList && item.childList.length) this.setTreeName(item.childList)
      })
    },
    getPointTree() {
      getPointTree({
        parentSerialNum: `1${this.targetData.serialNum}`
      }).then((res) => {
        this.data[0].childList = res.data
        this.setTreeName(this.data[0].childList)
        this.$refs.tree.setCurrentKey(-1)
      })
    },
    handleNodeClick(data) {
      this.parentSerialNum = data.serialNum
      this.getList(1)
    },
    back() {
      this.$emit('changePage', { type: 'firstPage', data: { page: '1' }})
    },
    // 获取dts主机列表
    deviceDropdown() {
      deviceDropdown().then((res) => {
        this.dtsList = res.data
      })
    },
    // 获取光纤名称列表
    opticalFiberDropdown() {
      opticalFiberDropdown({ deviceId: this.formData.deviceId }).then((res) => {
        this.opticalFiberList = res.data
      })
    },
    // 获取光纤段列表
    getPointParaList() {
      this.distanceList = []
      paragraphDropdown({ cableId: this.formData.cableId }).then((res) => {
        this.distanceList = res.data
      })
    },
    // 空间级联下拉框
    spaceCascadeDropdown() {
      const params = {
        serialNum: `1${this.targetData.serialNum}`
      }

      workFaceList(params).then((res) => {
        this.spaceList = res.data
      })
    },
    // dts主机选择改变
    dtsChange() {
      this.formData.cableId = ''
      this.formData.paragraphId = ''
      this.formData.startPosition = null
      this.formData.endPosition = null
      this.opticalFiberDropdown()
    },
    // 光纤名称选择改变
    cableIdChange() {
      this.formData.paragraphId = ''
      this.formData.startPosition = null
      this.formData.endPosition = null
      this.getPointParaList()
    },
    // 获取列表
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
        this.selectList = []
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {
          parentSerialNum: this.parentSerialNum || `1${this.targetData.serialNum}`
        },
        keyword: this.keyword
      }
      this.loading = true
      pointPage(params).then((res) => {
        this.tableData = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    },
    // 多选改变
    handleSelectionChange(list) {
      this.selectList = deepClone(list)
    },
    // 新增
    add() {
      this.titleName = '新增'
      this.workFace = null

      this.dialogVisible = true
      this.$refs.form?.clearValidate()
    },
    // 详情、编辑
    getDetail(e, type) {
      this.isCheck = type === '详情'
      this.titleName = type
      this.formData = deepClone(e)
      this.changeWorkFace(e.workFaceId)
      if (this.formData.serialNum) {
        this.formData.parentSerialNum = this.formData.serialNum.slice(0, 7)
      }
      // 截取序列号最后两位
      this.formData.serialNum = this.formData.serialNum.slice(-2)
      this.formData.startPosition /= 100
      this.formData.endPosition /= 100
      this.formData.historyStartPosition /= 100
      this.formData.historyEndPosition /= 100

      this.opticalFiberDropdown()
      this.getPointParaList()
      this.spaceCascadeDropdown()
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.form?.clearValidate()
      })
    },
    // 提交
    handleSubmit() {
      const {
        historyStartPosition, historyEndPosition, startPosition, endPosition
      } = this.formData
      if (startPosition === null || endPosition === null) {
        this.$message.warning('请输入位置信息')
        return
      }
      if (startPosition >= endPosition) {
        this.$message.warning('结束位置必须大于开始位置')
        return
      }

      if ((historyStartPosition > endPosition) || (historyEndPosition > endPosition)) {
        this.$message.warning('历史数据保存位置必须在位置范围内')
        return
      }
      if (!this.formData.points?.length) {
        this.$message.warning('请标绘')
        return
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          const data = {
            ...this.formData
          }
          const httpRqs = this.formData.id ? updatePoint : addPoint
          this.fullscreenLoading = true
          httpRqs(data).then((res) => {
            if (res.code === 200) {
              this.$message.success('成功')
              this.getList()
              this.closeDialog()
            }
          }).finally(() => {
            this.fullscreenLoading = false
          })
        }
      })
    },
    // 弹窗关闭
    closeDialog() {
      this.formData = {
        name: '', // 名称
        deviceId: '', // DTS主机
        cableId: '', // 光纤名称
        paragraphId: '', // 光纤段
        // batteryNum: '', // 电池编号
        workFaceId: '', // 工作面
        startPosition: null,
        endPosition: null
      }
      this.isCheck = false
      this.dialogVisible = false
    },
    // 删除
    del(e) {
      this.select = deepClone(e)
      this.dialogVisibleDel = true
    },
    // 批量删除
    batchDel() {
      if (!this.selectList.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.dialogVisibleDel = true
    },
    // 删除提交
    handleDel() {
      const data = (this.select && this.select.id) ? [this.select.id] : this.selectList.map((item) => item.id)
      delPoint(data).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          if ((this.select && this.select.id) && (this.pageNum > 1) && this.tableData.length === 1) {
            this.pageNum--
          } else if ((this.pageNum > 1) && this.tableData.length === this.selectList.length) {
            this.pageNum--
          }
          this.getList()
          this.closeDelDialog()
        }
      })
    },

    // 删除取消
    closeDelDialog() {
      this.select = {}
      this.dialogVisibleDel = false
    },
    // 批量导入
    batchImport() {
      this.importDialogVisible = true
    },
    closeImportDialog() {
      this.importDialogVisible = false
    },
    // 下载模板
    downloadTemplate() {
      downloadPointTemplate().then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '下载成功'
        })
      })
    },
    // 导入
    handleImportSubmit(file) {
      const blob = file.raw
      const type = blob.name.split('.')[1]
      if (type !== 'xlsx') {
        this.$message.warning(
          '导入文件只能是.xlsx结尾的文件，请检查上传文件是否是从本页面下载的模板'
        )
      } else {
        const formData = new FormData()
        formData.append('file', blob)
        this.uploading = true
        importPoint(formData, this.opticalFiberId)
          .then((res) => {
            this.importDialogVisible = false
            this.resultData = res.data
            this.dialogVisibleImport = true
          }).finally(() => {
            this.uploading = false
          })
      }
    },
    handleCloseImport() {
      this.getList()
      this.dialogVisibleImport = false
    },
    submitFormImport() {
      this.getList()
      this.dialogVisibleImport = false
    },
    // 下载失败数据
    downFail() {
      const oReq = new XMLHttpRequest()
      oReq.open('GET', this.resultData.failExcelUrl, true)
      oReq.responseType = 'arraybuffer'
      oReq.withCredentials = true
      // eslint-disable-next-line func-names
      oReq.onload = function() {
        const arraybuffer = oReq.response
        if (arraybuffer) {
          const byteBUffer = new Uint8Array(arraybuffer)
          const link = document.createElement('a')
          const blob = new Blob([byteBUffer], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
          link.href = URL.createObjectURL(blob)
          link.download = '导入失败数据.xlsx'
          link.click()
        }
      }
      oReq.send()
      this.$message({
        type: 'success',
        message: '下载成功'
      })
    },
    // 批量导出
    batchExport() {
      const data = {
        pageNum: 1, // 写死第一页
        pageSize: this.pageSize,
        query: {
          parentSerialNum: this.parentSerialNum
        },
        keyword: this.keyword
      }
      pointExport(data).then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '导出成功'
        })
      })
    },
    locate() {
      if (!this.workFace.image) {
        this.$message.warning('该工作面没有底图!')
        return
      }

      this.locateDialogVisible = true
    },
    numberInput(num) {
      const val = num.replace(/[^0-9]/g, '')
      return val
    },
    changeWorkFace(val) {
      this.workFace = this.spaceList.find((item) => item.id === val)
      this.segments = []
      this.pointLoading = true
      pointList(this.workFace.id).then((res) => {
        for (const item of res.data) {
          if (item.id === this.formData.id) continue
          if (!item.points?.length) continue
          this.segments.push(item.points)
        }
      }).finally(() => {
        this.pointLoading = false
      })
    },
    changePoint() {
      const { locateRef } = this.$refs
      if (!locateRef.validate()) return
      this.formData.points = locateRef.getPoints()
      this.locateDialogVisible = false
    }

  }
}
</script>

<style lang="scss" scoped>
.point_manage {
  font-family: PingFang SC RE;

  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #E5E5E5;

    .left {
      display: flex;
      align-items: center;
      font-size: 17px;
      font-weight: bold;

      img {
        width: 28px;
        height: 28px;
        cursor: pointer;
        margin-right: 10px;
      }

      .pa_name {
        color: #8D95A5;
        margin-right: 5px;
      }

      .ch_name {
        color: #202225;
        margin-right: 5px;
      }
    }
  }

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30px 0;
    width: 100%;

    .left {
      font-size: 17px;
      font-weight: bold;
      padding-left: 10px;
      color: #202225;
      border-left: 4px solid #1768EB;
    }

    .right {
      display: flex;
      width: 100%;
      justify-content: space-between;
    }
  }
}

.form_part {
  display: flex;
  flex-wrap: wrap;

  .form_item {
    width: 50%;
  }
}

.list_box {
  width: 16%;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #E0E0E0;
  margin-top: 28px;
}

.list_name {
  margin-top: 30px;
  height: 20px;
  font-size: 18px;
  font-family: PingFang SC RE;
  font-weight: bold;
  color: #202225;
  padding-left: 10px;
  border-left: 4px solid #1768EB;
}

</style>
