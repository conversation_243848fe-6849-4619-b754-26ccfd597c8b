import { Raycaster, Vector2 } from 'three'
import { canvasSize } from '@/views/aobo/fieldView/threeJs/service/core'

export class RayCasterController {
  camera

  // 储存所有的物体
  allObjects = []

  callbacks = new Map()

  tooltipRaycaster = new Raycaster()

  constructor(camera, domElement) {
    this.camera = camera
    this.tooltipRaycaster.far = 15
    this.domElement = domElement
    this.registerEvent()
  }

  registerEvent = () => {
    let downX = 0
    let downY = 0
    let preTime
    const onMousedown = (e) => {
      preTime = performance.now()
      downX = e.screenX
      downY = e.screenY
    }
    this.domElement.addEventListener('mousedown', onMousedown)
    const clickRaycaster = new Raycaster()
    const onMouseup = (e) => {
      if (e.button !== 0) return
      // console.log('mouseup - mousedown', performance.now() - preTime)

      const offsetX = Math.abs(e.screenX - downX)
      const offsetY = Math.abs(e.screenY - downY)
      if (offsetX > 1 || offsetY > 1) return
      const position = new Vector2((e.offsetX / canvasSize.width) * 2 - 1, 1 - (e.offsetY / canvasSize.height) * 2)
      clickRaycaster.setFromCamera(position, this.camera)
      const intersects = clickRaycaster.intersectObjects(this.allObjects)
      const firstObject = intersects?.[0]
      if (firstObject) {
        for (const item of this.callbacks.keys()) {
          if (item.includes(firstObject.object)) {
            const callback = this.callbacks.get(item)
            callback(firstObject.object, firstObject.point, firstObject?.instanceId || -1)
            // console.log('clickRaycaster', performance.now() - preTime)
            break
          }
        }
      }
    }
    this.domElement.addEventListener('mouseup', onMouseup)
  }

  bindClickRayCastObj = (raycastObjects = [], onClick) => {
    this.allObjects = this.allObjects.concat(raycastObjects)
    this.callbacks.set(raycastObjects, onClick)

    return () => {
      this.allObjects = this.allObjects.filter((item) => !raycastObjects.includes(item))
      this.callbacks.delete(raycastObjects)
    }
  }
}
