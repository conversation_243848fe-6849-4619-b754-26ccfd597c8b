import * as THREE from 'three'
import { Color, LoadingManager } from 'three'
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader'
import store from '@/store'
import { camera, orbitControls, postProcess, scene } from '@/views/aobo/fieldView/threeJs/service/core'
import linePoints from '@/assets/center-line/linePoints.json'
import A01 from '@/assets/center-line/A01.json'
import A02 from '@/assets/center-line/path_points.json'
import B01 from '@/assets/center-line/B01.json'
import B02 from '@/assets/center-line/B02.json'
import C01 from '@/assets/center-line/C01.json'
import C02 from '@/assets/center-line/C02.json'
import gsap from 'gsap'
import { RayCasterController } from '@/views/aobo/fieldView/threeJs/service/rayCasterController'

// console.log(C01.reverse(), 'B02.reverse()')
/**
 * 模型精度
 * */
const precision = localStorage.getItem('precision') || '1'
const precisionMap = {
  0: 'model/smooth.glb',
  1: 'model/high.glb',
  2: 'model/best.glb',
}

const gui = null
/**
 * 中轴线数据
 * */
const centerLineList = [
  // { name: '管廊', points: linePoints, color: 'blue' },
  { name: 'A01', points: A01, color: 'red' },
  { name: 'A02', points: A02, color: 'green' },
  { name: 'B01', points: B01, color: 'cyan' },
  { name: 'B02', points: B02, color: 'magenta' },
  { name: 'C01', points: C01, color: 'yellow' },
  { name: 'C02', points: C02, color: 'orange' },
]

export class Environment {
  loadingManager

  rgbeLoader

  // GLTF模型对象
  model

  singleCabinetModel

  // 加载进度
  progress = 0

  // 模型尺寸
  modelSize

  // 管廊中轴线
  centerCurve

  curveMap = {}

  tubeList = []

  // 存储一些常用资源
  assets = {
    mainBuilding: null,
  }

  textureObj = {
    tube: null,
    status: {},
  }

  constructor() {
    this.rgbeLoader = new RGBELoader(this.loadingManager)
    this.#initLoadingManager()
    this.#addLight()
    this.#loadModel().then(() => {
      this.#loadTexture().then(() => {
        this.#loadCenterLine()
      })
    })
    this.#createTubeTexture()

    // scene.add(new THREE.AxesHelper(100000))
  }

  /** 初始化加载管理器*/
  #initLoadingManager() {
    const loadingManager = new LoadingManager()
    this.loadingManager = loadingManager
    loadingManager.onLoad = function() {
      setTimeout(() => {
        store.dispatch('threeJs/setLoaded', true)
      }, 500)
    }

    loadingManager.onProgress = (item, loaded, total) => {
      this.progress = (loaded / total) * 100
      store.dispatch('threeJs/setProgress', this.progress)
    }

    loadingManager.onError = function(url) {
      console.log(`Error loading ${url}`)
    }
  }

  /**
   * 创建管道纹理
   * */
  #createTubeTexture() {
    const list = [
      { type: 0, color: ['#26c4a2', '#068f87'] },
      { type: 1, color: ['#ec9e66', '#ed7a27'] },
      { type: 2, color: ['#ecc166', '#ed9d27'] },
      { type: 3, color: ['rgba(243,167,167,0.7)', 'rgba(237,111,111,0.7)'] },
    ]

    for (const item of list) {
      const canvas = document.createElement('canvas')
      canvas.width = 256
      canvas.height = 256
      const ctx = canvas.getContext('2d')

      // 创建径向渐变
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
      gradient.addColorStop(0, item.color[0])
      gradient.addColorStop(0.45, item.color[1])
      gradient.addColorStop(0.55, item.color[1])
      gradient.addColorStop(1, item.color[0])

      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      const texture = new THREE.CanvasTexture(canvas)
      texture.colorSpace = THREE.SRGBColorSpace
      texture.wrapS = THREE.RepeatWrapping // U方向（沿管道长度）
      texture.wrapT = THREE.RepeatWrapping // V方向（围绕管道）
      texture.repeat.set(1, 4) // U方向重复5次，V方向不重复
      this.textureObj.status[item.type] = texture
    }
  }

  /** 加载纹理贴图*/
  #loadTexture() {
    const loader = new THREE.TextureLoader(this.loadingManager)
    return new Promise((resolve, reject) => {
      loader.load('texture/deng_002.jpg', (texture) => {
        texture.colorSpace = THREE.SRGBColorSpace
        // 移除等距柱状投影映射，使用默认的UV映射
        // texture.mapping = THREE.EquirectangularReflectionMapping

        // 设置纹理沿着管道长度方向重复 ClampToEdgeWrapping
        texture.wrapS = THREE.RepeatWrapping // U方向（沿管道长度）
        texture.wrapT = THREE.ClampToEdgeWrapping // V方向（围绕管道）

        // 调整重复次数，让纹理沿着管道长度方向平铺
        // 可以根据需要调整这个值来控制纹理的密度
        texture.repeat.set(23000, 0) // U方向重复5次，V方向不重复

        this.textureObj.tube = texture
        resolve(texture)
      })
    })
  }

  /** 加载环境纹理*/
  #loadEnvironmentTexture() {
    const hdrTexture = this.rgbeLoader.load('model/env.hdr', (texture) => {
      texture.colorSpace = THREE.SRGBColorSpace
      texture.mapping = THREE.EquirectangularReflectionMapping
      scene.environment = texture
      scene.background = texture
    })
  }

  /** 加载模型*/
  #loadModel() {
    const dracoLoader = new DRACOLoader()
    dracoLoader.setDecoderPath('draco/')
    const loader = new GLTFLoader(this.loadingManager)
    loader.setDRACOLoader(dracoLoader)
    return new Promise((resolve, reject) => {
      loader.load(precisionMap[precision], (gltf) => {
        const modelScene = gltf.scene
        scene.add(modelScene)

        this.singleCabinetModel = modelScene

        const changeMaterial = (mesh) => {
          mesh.castShadow = false
          mesh.receiveShadow = false
          // 销毁之前的材质，节约性能
          mesh.material.dispose()
          mesh.material = null
          mesh.material = new THREE.MeshLambertMaterial({
            transparent: true,
            // color: new THREE.Color(color),
            color: new THREE.Color('#1cbfd8'),
            // color: isDoor ? new THREE.Color('#ff6666') : new THREE.Color('#4C6B9A'),
            // opacity: mesh.parent.name.includes('管廊') ? 0.2 : 0.3,
            opacity: 0.3,
            side: THREE.DoubleSide,
          })
          if (mesh.parent.name.includes('管廊')) {
            // mesh.material.emissive = new THREE.Color('#1cbfd8')
            // mesh.material.emissiveIntensity = 5
          }
        }

        // 不需要更换材质的模型
        const noChangeMaterialList = ['电线']
        // 需要更换材质的模型
        // const nameList = ['管廊', '地形', '房屋', '线箱', '支架', '吊装']
        const nameList = ['管廊', '地形', '房屋', '线箱', '吊装']
        modelScene.traverse((child) => {
          if (child.name.includes('水管')) {
            child.visible = false
            // console.log(child, 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa')
          }
          if (child.name.includes('管廊')) {
            // child.visible = false
          }

          if (child.name.includes('线箱')) {
            // child.visible = false
          }

          if (child instanceof THREE.Mesh) {
            // 将材质金属度降低，以便其能接受环境光
            child.material.metalness = 0.3
            child.material.specularIntensity = 1
            child.geometry.computeBoundsTree()
          }
        })

        modelScene.children.forEach((child) => {
          for (const name of noChangeMaterialList) {
            if (child.name.includes(name)) {
              return
            }
          }

          let flag
          for (const name of nameList) {
            if (child.name.includes(name)) {
              flag = true
              break
            }
          }

          if (flag) {
            if (child instanceof THREE.Group) {
              for (const el of child.children) {
                if (el instanceof THREE.Mesh) {
                  changeMaterial(el)
                }
              }
            } else if (child instanceof THREE.Mesh) {
              changeMaterial(child)
            }
          }
        })
        // modelScene.updateMatrixWorld(true)
        dracoLoader.dispose()

        // 将视角定位到光缆起点
        const position = new THREE.Vector3(-539, -32, -9)
        orbitControls.target.copy(position)
        orbitControls.update()

        gsap.to(camera.position, {
          x: -614,
          y: -3,
          z: -38,
          duration: 1,
          ease: 'power2.inOut',
        })

        resolve(true)
      })
    })
  }

  /** 加载中轴线*/
  #loadCenterLine() {
    // 单独绘制管廊
    const centerCurve = new THREE.CatmullRomCurve3(linePoints.map((point) => new THREE.Vector3(point.x, point.y, point.z)))
    this.centerCurve = centerCurve
    this.tubeList = []

    for (const item of centerLineList) {
      const points = []

      for (const point of item.points) {
        points.push(new THREE.Vector3(point.x, point.y, point.z))
      }

      // 创建曲线
      const curve = new THREE.CatmullRomCurve3(points)
      this.curveMap[item.name] = curve

      // 绘制管道
      const tubeGeometry = new THREE.TubeGeometry(curve, 1225, 0.08, 14, false)
      tubeGeometry.computeBoundsTree()
      const tubeMaterial = new THREE.MeshPhongMaterial({
        map: this.textureObj.tube,
        side: THREE.DoubleSide,
        // roughness: 0.5,
        // metalness: 0.5,
        // specularIntensity: 1,
        shininess: 30,
        specular: new THREE.Color('#494646'),

      })
      const tube = new THREE.Mesh(tubeGeometry, tubeMaterial)
      tube.name = item.name
      // tube.visible = false
      this.tubeList.push(tube)
      scene.add(tube)
    }
  }

  // 存储光源引用以便清理
  lights = []

  #addLight() {
    // 环境光 - 提供基础照明
    const ambientLight = new THREE.AmbientLight(0xffffff, 2)
    scene.add(ambientLight)
    this.lights.push(ambientLight)

    // 方向光
    const directionalLight = new THREE.DirectionalLight(0xffffff, 8)
    directionalLight.position.set(10, 10, 5)
    scene.add(directionalLight)
    this.lights.push(directionalLight)

    // 第二个平行光
    const directionalLight2 = new THREE.DirectionalLight(0xffffff, 8)
    directionalLight2.position.set(-10, 10, -5)
    scene.add(directionalLight2)
    this.lights.push(directionalLight2)
  }

  dispose() {
    // 清理光源
    this.lights.forEach((light) => {
      if (light.parent) {
        light.parent.remove(light)
      }
      if (light.dispose) {
        light.dispose()
      }
    })
    this.lights = []

    // 清理加载器
    if (this.rgbeLoader) {
      this.rgbeLoader = null
    }

    // 清理加载管理器
    this.loadingManager = null
  }
}
