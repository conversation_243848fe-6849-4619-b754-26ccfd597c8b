<template>
  <div>
    <div class="segment-popup" style="transform: translateY(-50%)">
      <header class="title" style="display: flex; justify-content: space-between; align-items: center; pointer-events: auto;gap: 10px">
        <el-tooltip :content="data.name">
          <span class="truncate">{{ data.parentData.name }}</span>
        </el-tooltip>
        <i class="el-icon-circle-close" style="cursor: pointer" @click="close" />
      </header>
      <section class="content" style="pointer-events: auto">
        <div>
          <div>
            <span>位置：</span>
            <span>{{ data.parentData.parentCube }}-{{ Math.round(data.cablePosition) / 100 }}m处</span>
          </div>

          <div style="margin-top: 12px;">
            <span>所属测温段：</span>
            <span>{{ data.parentData.name }}</span>
          </div>

          <div style="margin-top: 12px;display: flex;align-items: center;gap: 60px">
            <div>
              <span>温度：</span>
              <span v-if="data.temperature !== null">{{ data.temperature / 100 }}℃</span>
              <span v-else>--</span>
            </div>
            <div>
              <span>告警等级：</span>
              <span v-if="data.level !== null" :style="{color: alarmColorMap[data.level]}">{{ alarmLevelMap[data.level] }}</span>
              <span v-else>--</span>
            </div>
          </div>

          <div style="margin-top: 12px; text-align: center">
            <el-button type="primary" @click="detail">详情</el-button>
          </div>

        </div>
      </section>
      <footer style="display: flex; justify-content: center; margin-top: 8px">
        <img src="@/assets/onSiteView/marker-alarm.webp" style="width: 38px; height: 42px" class="marker" />
      </footer>
    </div>
  </div>
</template>

<script>
import { alarmColorMap, alarmLevelMap } from '@/constants'

export default {
  name: 'SegmentPopup',
  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {

    }
  },
  computed: {
    alarmLevelMap() {
      return alarmLevelMap
    },
    alarmColorMap() {
      return alarmColorMap
    },
    temperature() {
      return this.data.temperature
    },
  },
  methods: {
    close() {
      this.$emit('close')
    },

    detail() {
      this.$emit('detail', this.data)
    },

  },
}
</script>
<style scoped lang="scss">
.segment-popup {
  .title {
    width: 327px;
    height: 44px;
    line-height: 44px;
    background: url(~@/assets/onSiteView/marker-bg-alarm.webp) center / 100% 100% no-repeat;
    color: #FF3126;
    font-size: 20px;
    font-weight: bold;
    padding-left: 76px;
    padding-right: 10px;
  }

  .content {
    padding: 12px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, #000 100%);
    color: white;
    font-size: 14px;
  }
}

</style>
