<template>
  <div v-loading="loading || !threeJsLoaded" class="main-content">
    <div class="top">
      <div style="display: flex; align-items: center">
        <div style="font-weight: bold">数据刷新间隔时间:</div>
        <el-select v-model="timeInterval" placeholder="请选择" filterable allow-create style="width: 100px; margin: 0 10px" @change="timeIntervalChange">
          <el-option v-for="item in timeList" :key="item" :label="item" :value="item" />
        </el-select>
        <div style="font-weight: bold">秒</div>
        <div style="margin-left: 20px">
          <span style="font-weight: bold">模型精度:</span>
          <el-select v-model="precision" placeholder="请选择" filterable allow-create style="width: 100px; margin: 0 10px" @change="precisionChange">
            <el-option v-for="(label, value) in precisionOption" :key="value" :label="label" :value="value" />
          </el-select>
        </div>
        <!--        <div style="margin-left: 20px;">-->
        <!--          <span>自动巡览：</span>-->
        <!--          <el-switch-->
        <!--            v-model="autoView"-->
        <!--            @change="changeAutoView"-->
        <!--          />-->

        <!--        </div>-->
      </div>
      <div style="display: flex; align-items: center">
        <el-slider v-model="position" show-input style="width: 500px;" :min="0" :max="maxPosition" @change="changePosition"></el-slider>
        <span style="margin-left: 8px;">m</span>
      </div>
      <div style="display: flex; align-items: center">
        <el-button style="margin-right: 20px" type="primary" @click="toHead">首部</el-button>
        <el-button style="margin-right: 20px" type="danger" @click="toEnd">尾部</el-button>
        <div style="fon-size: 15px; font-weight: bold; margin-right: 20px">
          {{ nowTime }}
        </div>
        <img style="width: 20px; height: 20px; cursor: pointer" src="@/assets/page/quanping.png" @click="handleFullScreen" />
      </div>
    </div>
    <div
      id="content"
      class="content"
      :class="isFullscreen ? 'full_content' : 'normal_content'"
      style="background-size: 100% 100% !important; background-repeat: no-repeat"
    >
      <div v-if="showFullAlarm" id="full-alarm" class="full_alarm">
        <div />
      </div>
      <ThreeJs ref="threeJsRef" :pipeline-list="segmentInfo.cables" />
      <div class="left_top">
        <div v-if="segmentInfo.refreshTime" class="time" style="margin-top: 20px">
          <img src="@/assets/page/<EMAIL>" />
          <div>{{ `当前数据采集时间：${segmentInfo.refreshTime || '---'}` }}</div>
        </div>
        <div v-if="deviceList" class="info_box">
          <div class="base_info">
            <template>
              <div v-for="item in deviceList" :key="item.id" style="margin-bottom: 18px">
                <div class="base_info_item">
                  <span style="color: #c4ddff">DTS主机信息：</span>
                  <span style="color: #ffffff">
                    {{ item.name || '--' }}
                  </span>
                </div>
                <div class="base_info_item">
                  <span style="color: #c4ddff">刷新时间：</span>
                  <span style="color: #ffffff">
                    {{ item.refreshInterval || item.refreshInterval === 0 ? `${item.refreshInterval}s` : '--' }}
                  </span>
                </div>
                <div class="base_info_item" style="margin-bottom: 3px">
                  <span style="color: #c4ddff">光缆信息：</span>
                </div>
                <template v-if="item.fiberList">
                  <div v-for="item1 in item.fiberList" :key="item1.id" style="margin-bottom: 10px; margin-left: 5px">
                    <div class="base_info_item">
                      <span style="color: #ffffff">{{ item1.name || '--' }}</span>
                    </div>
                    <div class="base_info_item">
                      <span style="color: #c4ddff">长度：</span>
                      <span style="color: #ffffff">
                        {{ item1.fiberLength || item1.fiberLength === 0 ? `${item1.fiberLength / 100}m` : '--' }}
                      </span>
                    </div>
                    <div class="base_info_item">
                      <span style="color: #c4ddff">纤芯类型：</span>
                      <span style="color: #ffffff">
                        {{ item1.fibreCoreSize || '--' }}
                      </span>
                    </div>
                  </div>
                </template>
              </div>
            </template>
          </div>
        </div>
      </div>

      <img v-if="isFullscreen" src="@/assets/page/tuichu.png" title="退出全屏" class="out_fullscreen" @click="handleFullScreen" />
      <AlarmBattery
        ref="alarmBattery"
        :is-fullscreen="isFullscreen"
        :alarm-list="segmentInfo.alarms"
        @detail="toAlarmLine"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import { deviceDropdown } from '@/api/lineCharts'
import Cookies from 'js-cookie'
import { playAudio } from '@/utils/palyAudio'
import { useDebounceFn } from '@/utils'
import AlarmBattery from '@/views/aobo/fieldView/components/alarmList/index'
import store from '@/store'
import { camera, environment, orbitControls } from '@/views/aobo/fieldView/threeJs/service/core'
import gsap from 'gsap'
import * as THREE from 'three'
import { segmentList } from '@/api/onSiteView'
import ThreeJs from './threeJs/index.vue'

// 报警持续闪烁时间（s）
const ALARM_TIME = 10
export default {
  name: 'FieldView',
  components: {
    ThreeJs,
    AlarmBattery,
  },
  data() {
    return {
      segmentInfo: { alarms: [] },
      autoView: false,
      btnAuthorityList: [], // 按钮权限
      nowTime: null,
      detailData: {},
      precisionOption: {
        0: '流畅',
        1: '高清',
        2: '极致',
      },
      precision: localStorage.getItem('precision') || '1',
      timeList: [30, 60, 90, 120], // 间隔时间下拉框
      timeInterval: Number(localStorage.getItem('timeIntervalOnSite')) || 60,
      intervalNow: null, // 当前数据采集时间
      isFullscreen: false, // 是否全屏
      showFullAlarm: false, // 是否展示全屏告警
      socket: null, // WebSocket
      socketCode: 1, // 状态码
      closeAudio: null,
      loading: false,
      timer: null, // 显示全屏报警定时器
      timer1: null,
      deviceList: [],
      position: 0,
      maxPosition: 1225,
    }
  },
  computed: {
    ...mapGetters(['threeJsLoaded', 'btnAuthority', 'threeJsProgress']),
  },
  created() {
    this.getSegmentInfo()
    const cur = this.btnAuthority.find((item) => item.code === this.$route.meta.code)
    if (cur) this.btnAuthorityList = cur.functionPermissionCode
    this.getDeviceList()
  },
  mounted() {
    this.websocketConnect()
    this.nowTime = dayjs().format('YYYY年MM月DD日 HH:mm:ss')
    this.intervalNow = setInterval(() => {
      this.nowTime = dayjs().format('YYYY年MM月DD日 HH:mm:ss')
    }, 1000)
  },
  beforeDestroy() {
    store.dispatch('threeJs/setLoaded', false)
    this.socket.close()
    this.socket = null
    this.closeAudio?.()
    clearInterval(this.intervalNow)
    clearTimeout(this.timer)
    clearTimeout(this.timer1)
  },
  methods: {
    getSegmentInfo(showLoading = true) {
      clearTimeout(this.timer1)
      if (showLoading) {
        this.loading = true
      }
      segmentList()
        .then((res) => {
          this.segmentInfo = res?.data || { alarms: [] }
          this.timer1 = setTimeout(() => {
            this.getSegmentInfo(false)
          }, this.timeInterval * 1000)
        })
        .finally(() => {
          this.loading = false
        })
    },
    /**
     * 获取设备列表
     * */
    getDeviceList() {
      deviceDropdown().then((res) => {
        this.deviceList = res?.data || []
      })
    },

    /**
     * 切换模型精度
     * */
    precisionChange() {
      localStorage.setItem('precision', this.precision)
      window.location.reload()
    },
    /**
     * 自动巡览
     * */
    changeAutoView(value) {
      if (!this.threeJsLoaded) {
        this.$message.warning('模型加载中')
        return
      }
      console.log('自动巡览', value)
      this.toHead().then(() => {
        const progress = { value: 0 }
        gsap.to(progress, {
          value: 0.99,
          duration: 60,
          ease: 'none',
          onUpdate: () => {
            const targetT = Math.min(progress.value + 0.01, 1)
            orbitControls.target.copy(environment.centerCurve.getPoint(targetT))
            orbitControls.update()
            const point = environment.centerCurve.getPoint(progress.value)
            console.log(point)
            camera.position.set(point.x, point.y, point.z)
          },
        })
      })
    },
    websocketConnect() {
      // 石伟
      this.socket = new WebSocket('/ws')

      // 监听socket连接
      this.socket.onopen = () => {
        const data = {
          code: this.socketCode,
          token: Cookies.get('governance_token'),
        }
        this.socket.send(JSON.stringify(data))
      }
      // 监听socket错误信息
      this.socket.onerror = (err) => {
        console.log(err, 'err')
      }
      // 消息防抖处理
      const debouncedFn = useDebounceFn((msg) => {
        const res = JSON.parse(msg.data)
        if (res.code === 200 && this.socketCode === 1) {
          this.socketCode++
          const data = {
            code: this.socketCode,
            msgId: res.message,
          }
          this.socket.send(JSON.stringify(data))
        } else if (res.code === 200 && res.message) {
          const { data } = res
          this.showFullAlarm = true
          if (data.isEnableAudio) {
            this.closeAudio?.()
            this.closeAudio = playAudio(data.audioUrl, data.cycleCount)
          }

          this.getSegmentInfo(false)

          clearTimeout(this.timer)
          this.timer = setTimeout(() => {
            this.showFullAlarm = false
          }, ALARM_TIME * 1000)
        }
      }, 1000)
      // 监听socket消息
      this.socket.onmessage = debouncedFn
      this.socket.onclose = () => {
        if (this.$route.meta.code !== 'XCST') return
        this.socketCode = 1
        this.socket = null
        this.websocketConnect()
      }
    },

    // 选择数据刷新间隔时间
    timeIntervalChange() {
      localStorage.setItem('timeIntervalOnSite', this.timeInterval)
      this.getSegmentInfo(false)
    },
    handleFullScreen() {
      this.isFullscreen = !this.isFullscreen
      const myEvent = new Event('resize')
      window.dispatchEvent(myEvent)
    },

    /**
     * 跳转到告警监控段
     * */
    toAlarmLine(row) {
      this.$refs.threeJsRef.goToAlarmMarker(row.id)
    },
    toHead() {
      if (!this.threeJsLoaded) {
        this.$message.warning('模型加载中')
        return
      }

      const position = new THREE.Vector3(-539, -32, -9)
      orbitControls.target.copy(position)
      orbitControls.update()
      return new Promise((resolve, reject) => {
        gsap.to(camera.position, {
          x: -614,
          y: -3,
          z: -38,
          duration: 1,
          ease: 'power2.inOut',
          onComplete: () => {
            resolve()
          },
        })
      })
    },
    toEnd() {
      if (!this.threeJsLoaded) {
        this.$message.warning('模型加载中')
        return
      }

      const position = new THREE.Vector3(539, -8, -86.8)
      orbitControls.target.copy(position)
      orbitControls.update()
      gsap.to(camera.position, {
        x: 573,
        y: 28,
        z: -191,
        duration: 1,
        ease: 'power2.inOut',
      })
    },
    /** 滑动改变模型位置*/
    changePosition(val) {
      const t = val / this.maxPosition
      this.$refs.threeJsRef.toPosition(t)
    }
  },
}
</script>

<style lang="scss" scoped>
@keyframes blink {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}
.main-content {
  //height: calc(100%);
  padding-top: 30px;
  box-sizing: border-box;
  font-family: PingFang SC RE;
  .top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .normal_content {
    width: 100%;
    height: calc(100vh - 230px);
    position: relative;
  }
  .full_content {
    width: 1920px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
  }
  .content {
    background: url(~@/assets/page/model_bg.webp) no-repeat;
    background-size: 100% 100%;
    z-index: 999;
    #full-alarm::before,
    #full-alarm::after {
      content: '';
      position: absolute;
      width: 120px;
      height: 100%;
    }
    #full-alarm > div::before,
    #full-alarm > div::after {
      content: '';
      position: absolute;
      width: 100%;
      height: 120px;
    }
    #full-alarm::before {
      background: linear-gradient(to right, red, transparent);
      top: 0;
      left: 0;
      transform: rotate(0deg);
    }
    #full-alarm::after {
      background: linear-gradient(to left, red, transparent);
      top: 0%;
      left: 100%;
      transform: rotate(0deg) translate(calc(-1 * 120px), 0px);
    }
    #full-alarm > div::before {
      background: linear-gradient(to top, red, transparent);
      top: 0;
      left: 0;
      transform: rotate(180deg);
    }
    #full-alarm > div::after {
      background: linear-gradient(to top, red, transparent);
      top: 100%;
      left: 0;
      transform: rotate(0deg) translate(0px, calc(-1 * 120px));
    }
    #full-alarm {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      opacity: 1;
      transition: opacity 0.5s;
      pointer-events: none;
      z-index: 999;
      animation: blink 1s infinite;
    }
    .left_top {
      position: absolute;
      left: 20px;
      top: 10px;
      z-index: 999;
      .room_title {
        display: flex;
        align-items: center;
        // height: 40px;
        margin-top: -10px;
        .img1 {
          width: 50px;
          height: 60px;
          margin-top: 15px;
        }
        .img2 {
          width: 25px;
          height: 20px;
          margin: 0 5px;
        }
        div {
          // font-size: 22px !important;
          // padding-top: 10px;
          padding-right: 5px;
          font-family: PingFang SC;
          font-weight: bold;
          background: linear-gradient(0deg, #0279fa 25%, #e8eeff 75%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .time {
        display: flex;
        margin-bottom: 20px;
        align-items: center;
        img {
          width: 15px;
          height: 15px;
          margin-right: 10px;
        }
        div {
          color: #91a0b4;
          font-size: 13px;
          font-weight: bold;
          display: flex;
        }
      }
      .info_box {
        width: 250px;
        background: url('../../../assets/page/kuang1.png') no-repeat;
        background-size: 100% 100%;
        padding: 20px 23px !important;
        .base_info {
          height: 300px !important;
          overflow: auto;
          .base_info_item {
            line-height: 27px;
            font-size: 13px;
            // font-weight: bold;
            // font-family: PingFang SC RE;
          }
        }
      }
    }
    .left_bottom {
      position: absolute;
      left: 20px;
      bottom: 30px;
      width: 220px;
      height: 65px;
      z-index: 999;
      background: url('../../../assets/page/kuang2.png') no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-between;
      font-size: 13px;
      .left_bottom_item {
        width: 25%;
        height: 90%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        position: absolute;
        > :first-child {
          font-size: 16px;
        }
        div {
          // font-weight: bold;
        }
      }
      .low {
        top: -12px;
        left: 0;
        div:nth-child(1) {
          background: linear-gradient(0, #5bd943 0%, #ffffff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: bold;
        }
      }
      .center {
        top: -12px;
        left: 40%;
        div:nth-child(1) {
          background: linear-gradient(0, #fab237 0%, #ffffff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: bold;
        }
      }
      .hign {
        top: -12px;
        right: 0;
        div:nth-child(1) {
          background: linear-gradient(0, #e53730 0%, #ffffff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          font-weight: bold;
        }
      }
    }
    .out_fullscreen {
      position: absolute;
      right: 50px;
      top: 20px;
      width: 50px;
      height: 50px;
      cursor: pointer;
      z-index: 999 !important;
    }
  }
  .dialog_footer {
    display: flex;
    justify-content: center;
    // margin-top: 20px;
  }
}
</style>
<style lang="scss">
.base_info {
  &::-webkit-scrollbar {
    width: 5px !important;
    cursor: pointer;
  }

  &::-webkit-scrollbar-track {
    height: 10px !important;
    width: 5px !important;
    background: rgba(81, 120, 192, 0.3);
    border-radius: 2px;
    cursor: pointer;
  }

  &::-webkit-scrollbar-thumb {
    height: 10px !important;
    width: 5px !important;
    background: rgba(30, 107, 248, 0.3);
    border-radius: 10px;
    cursor: pointer;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(19, 77, 184, 0.3);
    cursor: pointer;
  }

  &::-webkit-scrollbar-corner {
    background: rgba(27, 95, 225, 0.3);
  }
}
.pagination {
  // width: 250px !important;
  margin-top: 20px !important;
  .el-pagination__total {
    min-width: 25px !important;
    height: 25px !important;
    line-height: 25px !important;
    color: rgb(196, 196, 196) !important;
    font-size: 12px !important;
  }
  button {
    background: transparent !important;
    color: rgb(196, 196, 196) !important;
    border: 1px solid #686a6b !important;
    min-width: 23px !important;
    height: 25px !important;
  }
  .number {
    background: transparent !important;
    color: rgb(196, 196, 196) !important;
    border: 1px solid #686a6b !important;
    min-width: 23px !important;
    height: 25px !important;
    line-height: 25px !important;
    margin: 0 3px !important;
    font-size: 12px !important;
  }
  .active {
    background: #006be4 !important;
    border: none !important;
    min-width: 23px !important;
    height: 25px !important;
    line-height: 25px !important;
  }
  .more {
    background: transparent !important;
    border: 1px solid #686a6b !important;
    min-width: 23px !important;
    height: 25px !important;
    line-height: 25px !important;
  }
}

.no_data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: calc(100vh - 200px);
  img {
    width: 250px;
    height: 200px;
  }
  div {
    font-size: 20px;
    font-weight: bold;
    color: #7d7d7d;
  }
}
.list_container {
  height: calc(100% - 130px);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}
</style>
