<template>
  <div>
    <div style="display: flex; align-items: center; flex-wrap: nowrap">
      <div style="flex-shrink: 0">
        <el-radio-group v-model="currentTab" @change="changeTab">
          <el-radio-button :label="1">截面分析</el-radio-button>
          <el-radio-button :label="2">曲线分析</el-radio-button>
        </el-radio-group>
        <span style="margin-left: 20px; white-space: nowrap">
          <span>数据采集时间：</span>
          <span>{{ refreshTime }}</span>
        </span>
      </div>
      <!-- 曲线分析模式切换 -->
      <div v-if="systemInfo.singleLineRetainTime && refreshTime && currentTab === 2" class="mode-switch-inline" style="margin-left: 20px">
        <el-radio-group v-model="viewMode" size="small" @change="viewModeChange">
          <el-radio-button label="realtime">实时</el-radio-button>
          <el-radio-button label="history">历史</el-radio-button>
        </el-radio-group>
        <!-- 历史模式时间轴 -->
        <template v-if="viewMode === 'history'">
          <span class="time-label">{{ formatTime(historyTimeRange[0]) }}</span>
          <el-slider
            v-model="currentHistoryTime"
            :min="historyTimeRange[0]"
            :max="historyTimeRange[1]"
            :step="300000"
            :show-tooltip="true"
            :format-tooltip="formatTime"
            style="width: 250px; margin: 0 10px"
            @change="getHistoryData"
          />
          <span class="time-label">{{ formatTime(historyTimeRange[1]) }}</span>
        </template>
      </div>
    </div>

    <div style="margin-top: 12px">
      <HeatMapChart v-if="currentTab === 1" :temp-level-list="tempLevelList" :data="data" />
      <div v-else>
        <LineCharts v-if="showChart" ref="chartRef" v-loading="loading" />

        <div v-else class="no_data" style="height: 500px">
          <img src="@/assets/page/yypz_icon_zanwu.png" />
          <div>暂无数据</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { mapGetters } from 'vuex'
import LineCharts from '@/views/aobo/curveView/modules/lineCharts.vue'
import { liveTrend } from '@/api/lineCharts'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import HeatMapChart from './HeatMapChart.vue'

const alarmColorMap = {
  0: '#0000FF',
  1: '#FFA500',
  2: '#e4e46f',
  3: '#FF0000',
}

export default Vue.extend({
  name: 'MarkerDetail',
  components: { HeatMapChart, LineCharts },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    tempLevelList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: true,
      colorList: [
        { color1: '#5470c6', color2: 'rgba(84, 112, 198, 0)' },
        { color1: '#91cc75', color2: 'rgba(145, 204, 117, 0)' },
        { color1: '#fac858', color2: 'rgba(250, 200, 88, 0)' },
        { color1: '#ee6666', color2: 'rgba(238, 102, 102, 0)' },
        { color1: '#73c0de', color2: 'rgba(115, 192, 222, 0)' },
        { color1: '#3ba272', color2: 'rgba(59, 162, 114, 0)' },
        { color1: '#fc8452', color2: 'rgba(252, 132, 82, 0)' },
        { color1: '#9a60b4', color2: 'rgba(154, 96, 180, 0)' },
        { color1: '#ea7ccc', color2: 'rgba(234, 124, 204, 0)' },
      ],
      currentTab: 1,
      showChart: true,
      refreshTime: null,
      // 视图模式相关
      viewMode: 'realtime', // 'realtime' 或 'history'
      historyTimeRange: [], // 历史时间范围 [开始时间戳, 结束时间戳]
      currentHistoryTime: null, // 当前选中的历史时间
    }
  },
  computed: {
    ...mapGetters(['systemInfo']),
  },
  created() {
    this.changeTab(this.currentTab)
  },

  methods: {
    /**
     * 根据温度判断报警等级
     * */
    getAlarmLevel(temp) {
      if (!this.tempLevelList.length) return
      for (const item of this.tempLevelList) {
        if (temp >= item.value) {
          return item.level
        }
      }
    },

    getChartsData(isHistory = false) {
      const { parentData } = this.data
      const cablePosition = this.data.cablePosition || this.data.parentData.pointPosition * 100 || 0
      const data = {
        segmentIds: [parentData.id],
        // min: parentData.startPosition,
        // max: parentData.endPosition
      }

      // 如果是历史模式，添加time字段
      if (isHistory && this.currentHistoryTime) {
        data.time = dayjs(this.currentHistoryTime).format('YYYY-MM-DD HH:mm:ss')
      }
      this.loading = true
      // this.$refs.chartRef?.chart?.clear()
      liveTrend(data)
        .then((res) => {
          const chartsData = {
            xAxisData: [],
            seriesData: [],
          }
          let lineChartData = []
          if (!res.data || !res.data.segments.length) {
            return
          }
          const { segments } = res.data
          this.refreshTime = res.data.refreshTime
          lineChartData = segments
          // 判断哪一项有数据,如果都没有temperatures数据，则为暂无数据
          const haveDataArray = lineChartData.find((item) => (item.temperatures || []).length)
          if (haveDataArray) {
            chartsData.xAxisData = haveDataArray.temperatures.map((item) => item.position)
            this.showChart = true
          } else {
            this.showChart = false
            return
          }
          lineChartData.forEach((item) => {
            item.temperatures.forEach((item1) => {
              item1.fiberId = item.fiberId
              item1.startPosition = item.startPosition
            })
          })

          chartsData.seriesData = lineChartData.map((item, index) => {
            const datas = (item.temperatures || []).map((item1) => {
              item1.value = item1.temperature
              const alarmLevel = this.getAlarmLevel(item1.value)
              this.$set(item1, 'itemStyle', {
                color: alarmColorMap[alarmLevel] || '#00E667',
              })
              this.$set(item1, 'itemStyle', {
                color: alarmColorMap[alarmLevel] || '#00E667',
                opacity: alarmLevel !== undefined ? 1 : 0,
              })
              return [item1.position, (item1.value / 100).toFixed(2), item1]
            })
            const that = this
            // 将当前点位加入datas
            return {
              name: `${item.name}`,
              type: 'line',
              data: datas,
              smooth: true, // 平滑曲线
              sampling: 'average',
              large: true,
              // 添加一个markerLine, 用于标注当前点的位置
              markLine: {
                silent: true,
                data: [{ name: '当前点位', xAxis: cablePosition }],
                label: {
                  formatter: '当前点位',
                  position: 'insideEndBottom',
                },
              },
              symbolSize(params) {
                return that.getAlarmLevel(params[1]) !== undefined ? 8 : 0
              },
              itemStyle: {
                normal: {
                  color(params) {
                    // params 是当前数据点的信息
                    return alarmColorMap[that.getAlarmLevel(params.value[1])] || that.colorList[index].color1
                  },
                },
              },
              lineStyle: { width: 2.5, color: this.colorList[index].color1 },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: this.colorList[index].color2 },
                  { offset: 0.9, color: '#fff' },
                ]),
              },
              label: {
                show: false,
                position: 'top',
                formatter: (val) => `${(val.value / 100).toFixed(2)}℃`,
              },
            }
          })
          setTimeout(() => {
            this.$refs.chartRef?.updateOption(chartsData.seriesData)
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    changeTab(tab) {
      switch (tab) {
        case 1:
          break
        case 2:
          // 根据当前视图模式决定是否获取历史数据
          const isHistory = this.viewMode === 'history'
          this.getChartsData(isHistory)
          break
      }
      this.refreshTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    },

    /**
     * 初始化历史时间范围（当前时间减去数据保存时间）
     */
    initHistoryTimeRange() {
      // 使用当前数据采集时间作为结束时间
      const now = new Date(this.refreshTime).getTime()
      // 使用系统配置的数据保存时间
      const retainTime = this.systemInfo?.singleLineRetainTime
      const startTime = now - retainTime * 60 * 60 * 1000
      this.historyTimeRange = [startTime, now]
      this.currentHistoryTime = now // 默认选中当前时间
    },

    /**
     * 视图模式切换
     */
    viewModeChange() {
      if (this.currentTab === 2) {
        // 只在曲线分析tab时执行
        if (this.viewMode === 'realtime') {
          // 切换到实时模式
          this.getChartsData(false)
        } else {
          // 切换到历史模式
          this.initHistoryTimeRange()
          this.getHistoryData()
        }
      }
    },

    /**
     * 格式化时间戳为可读格式
     */
    formatTime(timestamp) {
      return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
    },

    /**
     * 获取历史数据
     */
    getHistoryData() {
      // 根据 currentHistoryTime 获取对应时间点的历史数据
      this.getChartsData(true) // 传入true表示获取历史数据
    },
  },
})
</script>

<style scoped lang="scss">
.mode-switch-inline {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap;
  overflow-x: auto;

  .time-label {
    font-size: 11px;
    color: #909399;
    white-space: nowrap;
    min-width: 70px;
    text-align: center;
    flex-shrink: 0;
  }

  .el-radio-group {
    flex-shrink: 0;
  }

  .el-slider {
    flex-shrink: 0;
  }
}
</style>
