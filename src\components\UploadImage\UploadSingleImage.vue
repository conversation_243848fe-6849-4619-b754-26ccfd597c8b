<template>
  <el-upload
    class="avatar-uploader"
    action="#"
    :auto-upload="false"
    :multiple="false"
    :show-file-list="false"
    :on-success="handleAvatarSuccess"
    accept=".jpg, .png, .jpeg, .svg, .webp, .ico"
    :before-upload="beforeAvatarUpload"
    :on-change="change"
  >
    <img
      v-if="imageUrl"
      :src="imageUrl"
      class="avatar"
    >
    <i
      v-else
      class="el-icon-plus avatar-uploader-icon"
    />
  </el-upload>
</template>

<script>

export default {
  name: 'UploadSingleImage',
  model: {
    prop: 'myValue',
    event: 'change'
  },
  props: {
    myValue: {
      require: true,
      type: String,
      default: ''
    }
  },
  computed: {
    imageUrl: {
      get() {
        return this.myValue
      },
      set(val) {
        this.$emit('change', val)
      }
    }
  },
  methods: {
    handleAvatarSuccess(res, file) {
      console.log(res, file)
      this.imageUrl = URL.createObjectURL(file.raw)
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG 格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    },
    change(file, fileList) {
      this.urlList = []
      const i = 0
      // 预览列表
      this.urlList.push(file.url)
      if (file.raw) {
        const rawFile = file.raw
        const reader = new FileReader()
        reader.readAsDataURL(rawFile)
        reader.onload = (e) => {
          this.imageUrl = e.target.result
          // 压缩图片
          // compressImage(e.target.result, 800, (res) => {
          //   this.imageUrl = res
          // })
        }
      }
    }
  }
}
</script>

<style lang="scss">
body {
  --upload-size: 178px;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: var(--upload-size);
  height: var(--upload-size);
  line-height: var(--upload-size);
  text-align: center;
}
.avatar {
  width: var(--upload-size);
  height: var(--upload-size);
  display: block;
  object-fit: cover;
}
</style>
