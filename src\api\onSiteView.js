import request from '@/utils/request'

// 分段列表
export function segmentList() {
  return request({
    url: '/api/v1/site',
    method: 'get'
  })
}

// 获取缆芯温度
export function getCoreTemp(data) {
  return request({
    url: '/api/v1/cable/coreTemp',
    method: 'get',
    params: data
  })
}

// 获取点位温度
export function getPointTemp(segmentId, position) {
  return request({
    url: '/api/v1/site/segment/point/temperature',
    method: 'get',
    params: { segmentId, position }
  })
}
