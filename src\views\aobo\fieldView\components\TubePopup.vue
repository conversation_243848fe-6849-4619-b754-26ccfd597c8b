<template>
  <div>
    <div class="segment-popup" style="transform: translateY(-50%)">
      <header class="title" style="display: flex; justify-content: space-between; align-items: center; pointer-events: auto;gap: 10px">
        <el-tooltip :content="data.name">
          <span class="truncate">{{ data.name }}</span>
        </el-tooltip>
        <i class="el-icon-circle-close" style="cursor: pointer" @click="close" />
      </header>
      <section class="content" style="pointer-events: auto;display: grid; grid-template-columns: repeat(2, 1fr); gap: 12px;">

        <div style="grid-column: span 2;">
          <span>位置：</span>
          <span>{{ data.name }}-{{ Math.round(data.pointPosition * 100) / 100 }}m处</span>
        </div>

        <div>
          <span>长度：</span>
          <span>{{ data.length / 100 }}m</span>
        </div>

        <div>
          <span>横截面积：</span>
          <span v-if="data.crossSectionalArea !== null">{{ data.crossSectionalArea }}mm²</span>
          <span v-else>--</span>
        </div>

        <div>
          <span>最高温度：</span>
          <span v-if="data.temperature !== null">{{ data.temperature }}℃</span>
          <span v-else>--</span>
        </div>

        <div>
          <span>最大电流：</span>
          <span v-if="data.current !== null">{{ data.maxCurrent }}A</span>
          <span v-else>--</span>
        </div>

        <div>
          <span>测温段数量：</span>
          <span>{{ (data.segments && data.segments.length) || 0 }}</span>
        </div>
      </section>
      <footer style="display: flex; justify-content: center; margin-top: 8px">
        <img src="@/assets/onSiteView/marker.webp" style="width: 38px; height: 42px" class="marker" />
      </footer>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TubePopup',
  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  methods: {
    close() {
      this.$emit('close')
    },
  },
}
</script>
<style scoped lang="scss">
.segment-popup {
  .title {
    width: 327px;
    height: 44px;
    line-height: 44px;
    background: url(~@/assets/onSiteView/title-bg.webp) center / 100% 100% no-repeat;
    color: #2187ff;
    font-size: 20px;
    font-weight: bold;
    padding-left: 76px;
    padding-right: 10px;
  }

  .content {
    padding: 12px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, #000 100%);
    color: white;
    font-size: 14px;
  }
}

</style>
