@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
  overflow: auto;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}

@font-face {
  font-family: 'PingFang SC RE';
  src: url('../font/PingFang\ Regular.ttf');
}

@font-face {
  font-family: 'Source Han Sans CN RE';
  src: url('../font/SourceHanSansCN-Medium.otf');
}

@font-face {
  font-family: 'Source Han Sans CN Regular';
  src: url('../font/SourceHanSansCN-Regular.otf');
}

@font-face {
  font-family: 'Title';
  src: url('../font/title_font.ttf');
}

// ----自定义统一样式 -start
.item-label {
  color: rgba(0, 0, 0, 0.85);
}

.item-info {
  color: rgba(0, 0, 0, 0.65);
}

.card-large {
  padding: 30px 40px;
  background: #fff;
  border-radius: 5px;
}

.page-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 80px;
  line-height: 80px;
  padding: 0 20px;
  box-shadow: 0px 0px 16px 4px rgba(15, 42, 104, 0.08);
  border-radius: 10px;
  margin-bottom: 20px;
  background: #fff;

  .page-top-title {
    font-size: 18px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #202020;
  }

  .filters {
    > * {
      margin-right: 20px;
    }
  }

  .buttons {
    margin-left: auto;
  }
}

.page-content {
  background: #ffffff;
  box-shadow: 0px 0px 16px 4px rgba(15, 42, 104, 0.08);
  border-radius: 6px;
}

.box-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 25px;
  color: #202020;

  &::before {
    content: '';
    display: inline-block;
    margin-right: 10px;
    width: 5px;
    height: 16px;
    background: linear-gradient(180deg, #50a7ff 0%, #ffffff 100%);
  }
}

.icon-in-input {
  font-weight: bold;
  cursor: pointer;
  // color: #0061CE
}

.infinite-list {
  height: calc(100vh - 300px);
  overflow-y: scroll;
}

.noMore {
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #b3b3b3;
}

.table-overflow-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

// 表格超过三行省略
.omit {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

// ----自定义统一样式 -end

// ----表单长短 -start
.medium-form-item {
  width: 200px !important;
}

.large-form-item {
  width: 300px !important;
}

// ----表单长短 -end

// ----ui规范 -start
.heading1 {
  font-size: 24px;
  font-weight: bold;
}

.heading2 {
  font-size: 20px;
  font-weight: bold;
  color: #212121;
}

.heading3 {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.tips-words {
  font-size: 13px;
  color: #999;
}

// ----ui规范 -end

// ----element-ui样式修改 -start
// 表格
.content-table {
  .table-header {
    th {
      background: rgba(219, 222, 233, 0.25) !important;
      font-size: 15px !important;
      font-family: PingFang SC RE;
      font-weight: bold;
      color: #202020;
      height: 30px !important;
      line-height: 30px !important;
      padding: 10px 0 !important;
      position: relative;

      // &:not(:last-child)::after {
      //   content: '';
      //   position: absolute;
      //   right: 0;
      //   top: 22px;
      //   width: 1px;
      //   height: 20px;
      //   background: #DDE1EE;
      // }
    }
  }

  td {
    height: 60px;
    line-height: 60px;
    font-size: 14px;
    font-family: Source Han Sans CN Regular;
    // font-weight: bold;
    color: #555555;
  }
}

// 滚动条样式
* {
  &::-webkit-scrollbar {
    height: 10px !important;
    width: 10px !important;
    cursor: pointer;
  }

  &::-webkit-scrollbar-track {
    height: 10px !important;
    width: 10px !important;
    background: rgb(239, 239, 239);
    border-radius: 2px;
    cursor: pointer;
  }

  &::-webkit-scrollbar-thumb {
    height: 10px !important;
    width: 10px !important;
    background: #bfbfbf;
    border-radius: 10px;
    cursor: pointer;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #888;
    cursor: pointer;
  }

  &::-webkit-scrollbar-corner {
    background: #d8d9db;
  }
}

.el-table::before {
  left: 0;
  bottom: 0;
  width: 100%;
  height: 0px;
}

.el-table__body-wrapper::-webkit-scrollbar {
  height: 10px !important;
  width: 10px !important;
  cursor: pointer;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background: #f1f4fb;
  border-radius: 2px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background: #d8d9db;
  border-radius: 10px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
  background: #c9c9ca;
}

.el-dialog {
  border-radius: 8px !important;

  .el-dialog__header {
    // background: #0061ce;
    background: #1768eb !important;
    border-top-left-radius: 8px !important;
    border-top-right-radius: 8px !important;
    padding: 10px 20px !important;

    .el-dialog__title {
      // color: #fff
      font-size: 16px !important;
      font-family: PingFang SC, PingFang SC !important;
      font-weight: bold !important;
      color: #fff !important;
    }

    .el-dialog__close {
      // color: #fff !important;
      font-size: 20px !important;
      font-weight: bold !important;
      color: #fff !important;
    }
    .el-dialog__headerbtn {
      top: 15px !important;
    }
  }

  .el-dialog__body {
    padding: 50px 20px;
  }

  .el-dialog__footer {
    // border-top: 1px solid #EBEBEB;
    padding-top: 0px !important;
    margin-top: 0 !important;
  }
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #fafbfc;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
  background: #f5f7fa;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background: #f1f5ff;
  color: #0581ff;
}

.el-table {
  margin-bottom: 15px;
}

.el-table .cell {
  white-space: pre-line;
}

.el-pagination {
  text-align: right;
}

.el-pagination.is-background .el-pager li {
  border: 1px solid #dfe1f1;
  background: rgba(255, 255, 255, 1);
  border-radius: 4px;
}

.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next {
  border: 1px solid #dfe1f1;
  background: rgba(255, 255, 255, 1);
}

.el-button {
  font-size: 14px !important;
}

.el-upload-list__item.is-success.focusing .el-icon-close-tip {
  display: none !important;
}

.el-submenu,
.el-menu-item {
  font-size: 15px !important;
  // min-width: 160px !important; //
}

.el-submenu__title {
  font-size: 16px;
}

.el-breadcrumb__inner {
  > a {
    color: #999;
  }
}

.el-popover--plain {
  white-space: pre-line;
}

body {
  .el-button {
    border-radius: 8px;
  }
}

// Tab、
.el-tabs__item {
  font-size: 16px !important;
  font-family: PingFang SC, PingFang SC !important;
  font-weight: 500 !important;
  color: #81859e !important;
  height: 60px !important;
  line-height: 60px !important;
}

.el-tabs__item.is-active {
  color: #1071e2 !important;
  font-size: 16px !important;
  font-family: PingFang SC, PingFang SC !important;
  font-weight: bold !important;
}

.el-tabs__item:hover {
  color: #1071e2 !important;
  font-size: 16px !important;
  font-family: PingFang SC, PingFang SC !important;
  font-weight: bold !important;
}

///////////穿梭框
::v-deep .el-checkbox__label {
  font-size: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  color: #202020;
}

// 列表（）
.el-table::before {
  left: 0;
  bottom: 0;
  width: 100%;
  height: 0px !important;
}

// ----element-ui样式修改 -end

.leaflet-container {
  font-size: 12px !important;
  path {
    outline: none;
  }
  .leaflet-tooltip {
    padding: 0;
    border: none;
  }
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
