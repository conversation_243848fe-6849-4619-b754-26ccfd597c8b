<template>
  <div class="first_page">
    <div class="title">
      驻点信息
    </div>
    <div class="base_info">
      <div class="space_name">{{ stagnationInfo ? stagnationInfo.projectName : '--' }}</div>
      <div class="base_info_content">
        <div class="base_info_item">
          <div class="dot" />
          <div>编号：{{ stagnationInfo.serialNum || '--' }}</div>
        </div>
        <div class="base_info_item">
          <div class="dot" />
          <div class="address">
            <div>地址：{{ stagnationInfo.address || '--' }}</div>
            <img
              v-if="stagnationInfo.address"
              src="@/assets/device_manage/icon_dingwei@2x .png"
              @click="showMap"
            >
          </div>
        </div>
        <div class="base_info_item">
          <div class="dot" />
          <div>经纬度：{{ stagnationInfo.lng ? `${stagnationInfo.lng}，${stagnationInfo.lat}` : '--' }}</div>
        </div>
      </div>
    </div>
    <el-tabs
      v-model="page"
      @tab-click="tabChange()"
    >
      <el-tab-pane
        v-if="isShowBatteryManage"
        label="煤矿管理"
        name="1"
      />
      <el-tab-pane
        v-if="isShowDtsManage"
        label="DTS管理"
        name="2"
      />
      <el-tab-pane
        v-if="isShowDtsManage"
        label="位移传感器"
        name="3"
      />
    </el-tabs>
    <div
      v-if="page === '1'"
      class="battery_room"
    >
      <el-button
        type="primary"
        size="small"
        style="margin: 0 0 5px 10px"
        :disabled="!btnAuthorityList.batteryManageBtn.includes('add')"
        @click="addRoom"
      >新增</el-button>
      <div class="room_list">
        <div
          v-for="item in roomList"
          :key="item.id"
          class="room_list_item"
        >
          <div class="name">
            <img src="@/assets/device_manage/icon_battery.png">
            <div>{{ item.name }}</div>
          </div>
          <div class="info">
            <div class="info_item">
              <div class="dot" />
              <span>煤矿编号：{{ item.serialNum || '--' }}</span>
            </div>
            <!--            <div class="info_item">-->
            <!--              <div class="dot" />-->
            <!--              <span>测温点数量：{{ item.checkPointCount || '&#45;&#45;' }}</span>-->
            <!--            </div>-->
            <div class="info_item">
              <div class="dot" />
              <span>报警阈值：{{ threshold ? `${(threshold / 100).toFixed(2)}℃` : '--' }}</span>
              <!-- <span>报警阈值：{{ threshold ? `${(threshold)}℃` : '--' }}</span> -->
            </div>
            <!-- <div class="info_item">
              <div class="dot" />
              <span>温度方差：{{ `${item.variance || '--'}℃` }}</span>
            </div> -->
          </div>
          <div class="bottom">
            <div class="left_btn">
              <el-button
                type="text"
                style="text-decoration:underline;"
                :disabled="!btnAuthorityList.batteryManageBtn.includes('cabinet-setting') && false"
                @click="goBatteryCab(item)"
              >工作面管理</el-button>
              <div class="line" />
              <el-button
                type="text"
                style="text-decoration:underline;"
                :disabled="!btnAuthorityList.batteryManageBtn.includes('point-setting') && false"
                @click="goPoint(item)"
              >监控段管理</el-button>
            </div>
            <div>
              <el-button
                type="success"
                size="mini"
                style="border-radius:20px !important;"
                :disabled="!btnAuthorityList.batteryManageBtn.includes('update')"
                @click="editBattery(item)"
              >编辑</el-button>
              <el-button
                type="danger"
                size="mini"
                style="border-radius:20px !important;"
                :disabled="!btnAuthorityList.batteryManageBtn.includes('del')"
                @click="delBattery(item)"
              >删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="page === '2'"
      class="dts_manage"
    >
      <div class="top">
        <el-input
          v-model="keyword"
          placeholder="请输入搜素内容"
          suffix-icon="el-icon-search"
          style="width:300px;"
          @blur="getList(1)"
        />
        <div class="btn_list">
          <el-button
            type="primary"
            size="small"
            :disabled="!btnAuthorityList.DTSManageBtn.includes('add')"
            @click="addDTS"
          >新增</el-button>
          <el-button
            type="danger"
            size="small"
            :disabled="!btnAuthorityList.DTSManageBtn.includes('batch-del')"
            @click="batchDel"
          >批量删除</el-button>
          <el-button
            type="success"
            size="small"
            :disabled="!btnAuthorityList.DTSManageBtn.includes('export')"
            @click="batchExport"
          >批量导出</el-button>
        </div>
      </div>
      <el-table
        ref="multipleTable"
        v-loading="loading"
        :header-cell-style="tableHeaderStyle"
        header-row-class-name="table-header"
        :data="dtsList"
        stripe
        :height="innerHeight - 490"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="70"
        />
        <el-table-column
          label="序号"
          type="index"
          width="70"
          align="center"
        />
        <el-table-column
          prop="name"
          label="DTS主机信息"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.name || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="mac"
          label="mac地址"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.mac || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="deviceNo"
          label="设备编号"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.deviceNo || '--' }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="resolutionRatio"
          label="采样率"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.resolutionRatio ? `${scope.row.resolutionRatio / 100}m` : '--' }}
          </template>
        </el-table-column> -->
        <el-table-column
          prop="refreshInterval"
          label="刷新时间"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>
              {{ scope.row.refreshInterval ? `${scope.row.refreshInterval}s` : '--' }}
            </span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="lineLength"
          label="光纤长度"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.lineLength ? `${scope.row.lineLength / 100}m` : '--' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="fibreCoreSize"
          label="纤芯类型"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.fibreCoreSize || '--' }}</span>
          </template>
        </el-table-column> -->
        <el-table-column
          prop="cableCount"
          label="光纤数量"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.cableCount || '--' }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column-->
        <!--          prop="pointCount"-->
        <!--          label="点位数量"-->
        <!--          show-overflow-tooltip-->
        <!--          align="center"-->
        <!--        >-->
        <!--          <template slot-scope="scope">-->
        <!--            <span>{{ scope.row.pointCount || scope.row.pointCount === 0 ?-->
        <!--              scope.row.pointCount : '&#45;&#45;' }}</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
        >
          <template v-slot="scope">
            <el-button
              type="text"
              style="text-decoration:underline;margin-right:15px;"
              @click="DTSDetail(scope.row)"
            >详情</el-button>
            <el-button
              type="text"
              style="text-decoration:underline;margin-right:15px;"
              :style="{color: !btnAuthorityList.DTSManageBtn.includes('update') ? '' : '#67C23A'}"
              :disabled="!btnAuthorityList.DTSManageBtn.includes('update')"
              @click="editDTS(scope.row)"
            >编辑</el-button>
            <el-button
              type="text"
              style="text-decoration:underline;margin-right:15px;"
              :disabled="!btnAuthorityList.DTSManageBtn.includes('update')"
              @click="opticalFiberManage(scope.row)"
            >光纤管理</el-button>
            <el-button
              type="text"
              style="text-decoration:underline;"
              :style="{color: !btnAuthorityList.DTSManageBtn.includes('del') ? '' : '#F56C6C'}"
              :disabled="!btnAuthorityList.DTSManageBtn.includes('del')"
              @click="delDTS(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="total,prev, pager, next,sizes, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <DisplacementSensor
      v-if="page === '3'"
      :btn-authority-list="btnAuthorityList"
    />

    <!-- 删除 -->
    <el-dialog
      title="删除"
      :visible.sync="dialogVisibleDel"
      :modal-append-to-body="false"
      width="500px"
      top="300px"
      @close="closeDelDialog()"
    >
      <div style="display:flex;align-items:center;padding-left:50px">
        <img
          src="@/assets/<EMAIL>"
          style="width:20px;height:20px;margin-right:10px"
        >
        <div style="font-size:16px;color:#F94E4E">
          确认删除所选数据吗？
        </div>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button @click="closeDelDialog()">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel()"
        >确 认</el-button>
      </div>
    </el-dialog>

    <!-- 新增、编辑煤矿 -->
    <el-dialog
      :title="formData.id || formData.id === 0 ? '编辑' : '新增'"
      :visible.sync="dialogVisible"
      width="500px"
      :modal-append-to-body="false"
      top="300px"
      @close="closeAddDialog()"
    >
      <el-form
        ref="form"
        :model="formData"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item
          label="煤矿编号:"
          prop="serialNum"
        >
          <el-input
            v-model="formData.serialNum"
            placeholder="请输入"
            size="large"
            style="width:300px;"
            :disabled="formData.id || formData.id === 0"
            @keyup.native="
              formData.serialNum = numberInput(
                formData.serialNum
              )
            "
          />
        </el-form-item>
        <el-form-item
          label="煤矿名称:"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            placeholder="请输入"
            size="large"
            style="width:300px;"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeAddDialog()"
        >取 消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit()"
        >确 定</el-button>
      </div>
    </el-dialog>

    <!-- 地图信息 -->
    <el-dialog
      title="地址"
      :visible.sync="mapDialogVisible"
      width="fit-content"
      :modal-append-to-body="false"
      top="15vh"
    >
      <div
        class="map-container"
      >
        <div
          ref="TMap"
          class="map"
        />
      </div>
    </el-dialog>

    <!-- 新增、编辑DTS -->
    <el-dialog
      :title="isCheck ? '详情' :
        formDataDTS.id || formDataDTS.id === 0 ? '编辑' : '新增'"
      :visible.sync="dialogVisibleDTS"
      width="500px"
      :modal-append-to-body="false"
      top="300px"
      @close="closeAddDialog()"
    >
      <el-form
        ref="formDTS"
        :model="formDataDTS"
        :rules="rulesDTS"
        :disabled="isCheck"
        label-width="120px"
      >
        <el-form-item
          label="DTS主机信息:"
          prop="name"
        >
          <el-input
            v-model="formDataDTS.name"
            placeholder="请输入"
            size="large"
            style="width:300px;"
          />
        </el-form-item>
        <!--        <el-form-item-->
        <!--          label="mac地址:"-->
        <!--          prop="mac"-->
        <!--        >-->
        <!--          <el-input-->
        <!--            v-model="formDataDTS.mac"-->
        <!--            placeholder="请输入"-->
        <!--            size="large"-->
        <!--            style="width:300px;"-->
        <!--          />-->
        <!--        </el-form-item>-->
        <el-form-item
          label="设备编号:"
          prop="deviceNo"
        >
          <el-input-number
            v-model="formDataDTS.deviceNo"
            placeholder="请输入"
            size="large"
            :min="0"
            :max="9"
            :controls="false"
            :disabled="formDataDTS.id"
            style="width:300px;"
          />
        </el-form-item>
        <el-form-item
          label="刷新时间:"
          prop="refreshInterval"
        >
          <el-input
            v-model="formDataDTS.refreshInterval"
            placeholder="请输入"
            size="large"
            style="width:300px;"
            @keyup.native="
              formDataDTS.refreshInterval = numberInput(
                formDataDTS.refreshInterval
              )
            "
          >
            <span
              slot="suffix"
              style="margin-right:10px;color:black;"
            >S</span>
          </el-input>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeAddDialog()"
        >取 消</el-button>
        <el-button
          type="primary"
          @click="handleSubmitDTS()"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  detail,
  areaList,
  // addArea,
  saveArea,
  delSpace,
  devicePage,
  delDevice,
  deviceExport,
  saveDtsDevice
} from '@/api/deviceManage'
import { dtsAlarmThreshold } from '@/api/lineCharts'
import { deepClone, utf8to16 } from '@/utils'
import tmap from '@/api/skymap/map'
import DisplacementSensor from '@/views/aobo/deviceManage/modules/DisplacementSensor.vue'

export default {
  name: 'DeviceManage',
  components: { DisplacementSensor },
  props: {
    btnAuthorityList: {
      type: Object,
      default: () => {}
    },
    isShowBatteryManage: {
      type: Boolean,
      default: false
    },
    isShowDtsManage: {
      type: Boolean,
      default: false
    },
    targetData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      stagnationInfo: {}, // 驻点信息
      // 煤矿
      page: '1',
      roomList: [], // 煤矿列表
      threshold: null, // 报警阈值
      select: null, // 选中数据
      dialogVisibleDel: false,
      formData: {
        name: '',
        serialNum: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入煤矿名称', trigger: 'blur' }
        ],
        serialNum: [
          { required: true, message: '请输入煤矿编号', trigger: 'blur' }
        ]
      },

      // DTS
      keyword: '',
      loading: false,
      dtsList: [], // dts列表
      pageNum: 1,
      pageSize: 10,
      total: 0,
      selectList: [], // 选中数据
      formDataDTS: {
        name: '',
        mac: '',
        refreshInterval: ''
      },
      dialogVisibleDTS: false,
      rulesDTS: {
        name: [
          { required: true, message: '请输入DTS主机信息', trigger: 'blur' }
        ],
        // mac: [{ required: true, message: '请输入mac地址', trigger: 'blur' }],
        deviceNo: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
        refreshInterval: [
          { required: true, message: '请输入刷新时间', trigger: 'blur' }
        ]
      },
      isCheck: false,

      // 弹窗
      dialogVisible: false,
      mapDialogVisible: false, // 地图弹窗
      T: null,
      map: null,
      innerHeight: null
    }
  },
  computed: {
    ...mapGetters(['tableHeaderStyle'])
  },
  mounted() {
    this.innerHeight = window.innerHeight
    window.onresize = () => {
      this.innerHeight = window.innerHeight
    }
    this.getStagnationInfo()
    this.dtsAlarmThreshold()
    if (this.targetData.page === '2' || !this.isShowBatteryManage) {
      this.page = '2'
      this.getList(1)
      return
    }
    this.getBatteryList()
  },
  methods: {
    // 获取驻点信息
    getStagnationInfo() {
      detail().then((res) => {
        this.stagnationInfo = res.data
      })
    },
    showMap() {
      this.mapDialogVisible = true
      tmap.then(() => {
        this.T = window.T
        this.map = new this.T.Map(this.$refs.TMap, {
          minZoom: 6,
          maxZoom: 18,
          zoom: 12,
          center: this.T.LngLat(this.stagnationInfo.lng, this.stagnationInfo.lat)
          // center: this.T.LngLat(116.40769, 39.89945)
        })
        const lnglat = new this.T.LngLat(this.stagnationInfo.lng, this.stagnationInfo.lat)
        // const lnglat = new this.T.LngLat(116.40769, 39.89945)
        this.marker = new this.T.Marker(lnglat)
        this.map.addOverLay(this.marker)
      })
    },
    // 获取报警阈值
    dtsAlarmThreshold() {
      dtsAlarmThreshold().then((res) => {
        this.threshold = res.data.threshold
        // this.threshold = 2000
      })
    },
    tabChange() {
      switch (this.page) {
        case '1':
          this.getBatteryList()
          break
        case '2':
          this.selectList = []
          this.dtsList = []
          this.pageNum = 1
          this.keyword = ''
          this.getList(1)
          break
      }
    },
    // 获取煤矿列表
    getBatteryList() {
      areaList().then((res) => {
        this.roomList = res.data.map((item) => {
          item.serialNum = item.serialNum.slice(1, 4)
          return item
        })
      })
    },
    // 工作面管理
    goBatteryCab(e) {
      this.$emit('changePage', {
        type: 'batteryCabManage',
        data: e
      })
    },
    // 监控段管理
    goPoint(e) {
      this.$emit('changePage', {
        type: 'pointManage',
        data: e
      })
    },
    // 新增煤矿
    addRoom() {
      this.dialogVisible = true
    },
    // 编辑煤矿
    editBattery(e) {
      this.formData = deepClone(e)
      this.dialogVisible = true
    },
    // 煤矿提交
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // const httpRqs = this.formData.id ? saveArea : addArea
          saveArea(this.formData).then((res) => {
            if (res.code === 200) {
              this.$message.success('成功')
              this.getBatteryList()
              this.closeAddDialog()
            }
          })
        }
      })
    },
    closeAddDialog() {
      this.dialogVisible = false
      this.dialogVisibleDTS = false
      this.formData = {
        name: '',
        serialNum: ''
      }
      this.formDataDTS = {
        name: '',
        mac: '',
        refreshInterval: ''
      }
      if (this.$refs.form) this.$refs.form.clearValidate()
      if (this.$refs.formDTS) this.$refs.formDTS.clearValidate()
    },
    // 删除煤矿
    delBattery(e) {
      this.select = deepClone(e)
      this.dialogVisibleDel = true
    },
    // 确认删除
    handleDel() {
      if (this.page === '1') {
        const data = [this.select.id]
        delSpace(data).then((res) => {
          if (res.code === 200) {
            this.$message.success('成功')
            this.getBatteryList()
            this.closeDelDialog()
          }
        })
      } else {
        const data = (this.select && this.select.id) ? [this.select.id] : this.selectList.map((item) => item.id)
        delDevice(data).then((res) => {
          if (res.code === 200) {
            this.$message.success('成功')
            if ((this.select && this.select.id) && (this.pageNum > 1) && this.dtsList.length === 1) {
              this.pageNum--
            } else if ((this.pageNum > 1) && this.dtsList.length === this.selectList.length) {
              this.pageNum--
            }
            this.getList()
            this.closeDelDialog()
          }
        })
      }
    },
    // 取消删除
    closeDelDialog() {
      this.dialogVisibleDel = false
      this.select = {}
    },
    // 获取dts列表
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
        this.selectList = []
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {},
        keyword: this.keyword
      }
      this.loading = true
      this.dtsList = [
        { id: 1 }
      ]
      devicePage(params).then((res) => {
        this.dtsList = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    },
    // 多选改变
    handleSelectionChange(list) {
      this.selectList = deepClone(list)
    },
    // 新增DTS
    addDTS() {
      this.isCheck = false
      this.dialogVisibleDTS = true
      // const data = { title: '新增' }
      // this.$emit('changePage', {
      //   type: 'newDTS',
      //   data
      // })
    },
    // 新增DTS提交
    handleSubmitDTS() {
      this.$refs.formDTS.validate((valid) => {
        if (valid) {
          // const httpRqs = this.formData.id ? saveArea : addArea
          saveDtsDevice(this.formDataDTS).then((res) => {
            if (res.code === 200) {
              this.$message.success('成功')
              this.getList()
              this.closeAddDialog()
            }
          })
        }
      })
    },
    // 批量删除DTS
    batchDel() {
      if (!this.selectList.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.dialogVisibleDel = true
    },
    // 批量导出DTS
    batchExport() {
      const data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {},
        keyword: this.keyword
      }
      deviceExport(data).then((res) => {
        const url = window.URL.createObjectURL(
          new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8'
          })
        )
        const temp = res.headers['content-disposition']
          .split(';')[1]
          .split('filename=')[1]
        const index = temp.indexOf('.')
        const str = temp.substr(0, index)
        const fileName = `${utf8to16(unescape(str))}.xlsx`
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        link.click()
        this.$message({
          type: 'success',
          message: '导出成功'
        })
      })
    },
    // 详情DTS
    DTSDetail(e) {
      this.isCheck = true
      this.formDataDTS = deepClone(e)
      this.dialogVisibleDTS = true
      // const data = deepClone(e)
      // this.$set(data, 'isEdit', true)
      // this.$set(data, 'title', '详情')
      // this.$emit('changePage', {
      //   type: 'newDTS',
      //   data
      // })
    },
    // 编辑DTS
    editDTS(e) {
      this.isCheck = false
      this.formDataDTS = deepClone(e)
      this.dialogVisibleDTS = true
      // this.$set(data, 'title', '编辑')
      // this.$emit('changePage', {
      //   type: 'newDTS',
      //   data
      // })
    },
    // 光纤管理
    opticalFiberManage(e) {
      const data = deepClone(e)
      this.$set(data, 'title', '编辑')
      this.$emit('changePage', {
        type: 'opticalFiberManage',
        data
      })
    },
    // 删除DTS
    delDTS(e) {
      this.select = deepClone(e)
      this.dialogVisibleDel = true
    },
    // 报警处置
    handleAlarm(e) {
      this.dialogVisible = true
      this.select = e
    },
    // 只能输入数字
    numberInput(num) {
      const val = num.replace(/[^0-9]/g, '')
      return val
    }
  }

}
</script>

<style lang="scss" scoped>
.first_page{
  //height: 100%;
  width: 100%;
  font-family: Source Han Sans CN Regular;
  font-size: 16px;
  .title {
    height: 20px;
    font-size: 17px;
    font-family: PingFang SC RE;
    font-weight: bold;
    color: #202225;
    padding-left: 10px;
    border-left: 4px solid #1768EB;
    margin-bottom: 20px;
  }
  .base_info {
    width: 100%;
    border: 1px solid #E0E0E0;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 10px;
    .space_name {
      font-size: 16px;
      font-weight: bold;
      color: #202225;
      margin-bottom: 15px;
    }
    .base_info_content {
      display: flex;
      justify-content: space-between;
      width: 60%;
      .base_info_item {
        display: flex;
        align-items: center;
        height: 23px;
        .dot {
          width: 8px;
          height: 8px;
          border-radius: 8px;
          background: #0E73F3;
          margin-right: 10px;
        }
        .address {
          display: flex;
          align-items: center;
          img {
            width:14px;
            height:20px;
            margin-left:10px;
            cursor: pointer;
          }
        }
      }
    }
  }
  .battery_room {
    .room_list {
      height: calc(100vh - 410px);
      overflow-y: auto;
      display: flex;
      // justify-content: space-between;
      flex-wrap: wrap;
      padding: 10px;
      .room_list_item {
        width: 24%;
        height: 203px;
        box-shadow: 0 0 15px 0px rgba(0,29,64,0.12);
        margin-bottom: 20px;
        border-radius: 10px;
        margin-right: 15px;
        &:nth-child(4n) {
          margin-right: 0;
        }
        padding: 20px;
        .name {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          img {
            width: 25px;
            height: 22px;
            margin-right: 10px;
          }
          div {
            font-size: 16px;
            font-weight: bold;
            color: #202225;
          }
        }
        .info {
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          padding-left: 10px;
          margin-bottom: 20px;
          .info_item {
            width: 47%;
            height: 35px;
            display: flex;
            align-items: center;
            .dot {
              width: 8px;
              height: 8px;
              border-radius: 8px;
              background: rgba(14,115,243,0.3);
              margin-right: 10px;
            }
          }
        }
        .bottom {
          display: flex;
          justify-content: space-between;
          .left_btn {
            display: flex;
            align-items: center;
            .btn_text {
              cursor: pointer;
              text-decoration: underline;
            }
            .line {
              margin: 0 10px;
              width: 1px;
              height: 15px;
              background: #1768EB;
            }
          }
        }
      }
    }
  }
  .dts_manage {
    .top {
      display: flex;
      justify-content: space-between;
      margin: 10px 0 20px 0;
      .btn_list {
        display: flex;
      }
    }
  }
  .dialog_footer {
    display: flex;
    justify-content: center;
  }
}
.map-container {
  padding: 0 30px;
  .map {
    width: 800px;
    height: 500px;
  }
}
::v-deep .el-tabs__item {
  height: 50px !important;
  line-height: 50px !important;
}
</style>
