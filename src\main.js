import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
// import 'element-ui/lib/theme-chalk/index.css'
import '../theme/index.css' // 引入自定义主题
// import locale from 'element-ui/lib/locale/lang/en' // lang i18n

import '@/styles/index.scss' // global css

import * as echarts from 'echarts'
import BaiduMap from 'vue-baidu-map'
import VueDOMPurifyHTML from 'vue-dompurify-html'
import App from './App'
import store from './store'
import router from './router'
import './styles/element-ui.scss'
import './styles/element-variables.scss'

import '@/icons' // icon
import '@/permission'
import '@/utils/flexible'

Vue.use(VueDOMPurifyHTML)
Vue.prototype.$echarts = echarts // permission control
ElementUI.Dialog.props.closeOnClickModal.default = false

Vue.use(BaiduMap, {
  ak: 'VCQPo4lP8QWjOtoqiQwwH39PHjIxsHcz',
})

// set ElementUI lang to EN
Vue.use(ElementUI, { size: 'medium' })

Vue.config.productionTip = false

// eslint-disable-next-line no-new
new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App),
})
