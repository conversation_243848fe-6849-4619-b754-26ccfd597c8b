<script>
import Vue from 'vue'
import * as echarts from 'echarts'
import { getCoreTemp } from '@/api/onSiteView'
import { alarmColorMap } from '@/constants'

function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result
    ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16),
    }
    : null
}

// 颜色插值函数
function interpolateColor(color1, color2, ratio) {
  const c1 = hexToRgb(color1)
  const c2 = hexToRgb(color2)

  const r = Math.round(c1.r + (c2.r - c1.r) * ratio)
  const g = Math.round(c1.g + (c2.g - c1.g) * ratio)
  const b = Math.round(c1.b + (c2.b - c1.b) * ratio)

  return `rgb(${r}, ${g}, ${b})`
}

// 温度到颜色的映射函数
function getColorByTemperature(temp, _) {
  const colors = [
    { temp: 90, color: '#7D0F0D' }, // 深红色 - 最热
    { temp: 80, color: '#D32139' }, // 红色
    { temp: 70, color: '#FB8223' }, // 橙色
    { temp: 60, color: '#FFEE00' }, // 黄色
    { temp: 50, color: '#94F709' }, // 黄绿色
    { temp: 40, color: '#01EFF0' }, // 青色
    { temp: 30, color: '#02ADFF' }, // 深天蓝色
    { temp: 20, color: '#1946FA' }, // 蓝色
    { temp: 10, color: '#120085' }, // 深蓝色 - 最冷
  ]

  // 边界情况处理
  if (temp >= colors[0].temp) return alarmColorMap[3]
  if (temp <= colors[colors.length - 1].temp) return alarmColorMap[0]

  // 查找温度区间并进行线性插值
  for (let i = 0; i < colors.length - 1; i++) {
    if (temp <= colors[i].temp && temp >= colors[i + 1].temp) {
      const ratio = (colors[i].temp - temp) / (colors[i].temp - colors[i + 1].temp)
      return interpolateColor(colors[i].color, colors[i + 1].color, ratio)
    }
  }
}

function processTemperatureData(tempArray) {
  // 定义层级配置（从内到外）
  const layers = [
    { color: null, title: '内核' },
    { color: null, title: '内层' },
    { color: null, title: '中层' },
    { color: null, title: '外层' },
    { color: null, title: '表层' },
  ]

  // 计算每组的大小
  const groupSize = Math.ceil(tempArray.length / layers.length)

  // 分组并计算每组的平均温度
  const result = layers.map((layer, index) => {
    const startIndex = index * groupSize
    const endIndex = Math.min(startIndex + groupSize, tempArray.length)
    const group = tempArray.slice(startIndex, endIndex)

    // 计算平均温度并四舍五入
    const avgTemp = Math.round(
      group.reduce((sum, temp) => sum + temp, 0) / group.length
    )

    return {
      color: getColorByTemperature(avgTemp),
      temp: avgTemp,
      title: layer.title
    }
  })

  return result
}

export default Vue.extend({
  name: 'HeatMapChart',
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    tempLevelList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: true,
      tempList: [],
      colorList: [],
      groupList: [],
    }
  },
  created() {
    this.colorList = []
    for (const item of this.tempLevelList) {
      this.colorList.push({ color: alarmColorMap[item.level], temp: item.value })
    }
    this.getCoreTemp()
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    /**
     * 获取缆芯温度
     * */
    getCoreTemp() {
      this.loading = true
      getCoreTemp({ current: this.data.parentData.current || 0, surfaceTemp: Math.floor(this.data.temperature / 100) })
        .then((res) => {
          // this.tempList = Object.values(res.data)
          // 生成20个模拟数据
          this.tempList = [98, 85, 78, 72, 65, 60, 55, 50, 48, 45, 42, 40, 38, 35, 33, 30, 28, 25, 22, 20]
          this.groupList = processTemperatureData(this.tempList)
          this.initChart()
        })
        .finally(() => {
          this.loading = false
        })
    },

    initChart() {
      const chart = echarts.init(this.$refs.chartRef)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const temperature = params.data
            return `温度: ${Math.round(temperature * 100) / 100}°C<br/>电流: ${this.data.parentData.current}A`
          },
        },
        xAxis: {
          type: 'value',
          show: false,
          min: 0,
          max: 300,
        },
        yAxis: {
          type: 'value',
          show: false,
          min: 0,
          max: 300,
        },
        series: [
          {
            type: 'custom',
            coordinateSystem: 'cartesian2d',
            data: this.tempList,
            renderItem: (params, api) => {
              const w = api.getWidth()
              const h = api.getHeight()
              const temp = api.value(0)
              // 分成5组，每一组的数量
              const groupSize = Math.ceil(this.tempList.length / 5)
              // 每一份圆环长度
              const unitLength = Math.min(w, h) / 2 / (this.tempList.length + 1)

              // const colors = alarmColorMap

              const fillColor = getColorByTemperature(temp, this.colorList)

              return {
                type: 'sector',
                shape: {
                  cx: w / 2,
                  cy: h / 2,
                  r0: unitLength * params.dataIndex,
                  r: unitLength * (params.dataIndex + 1),
                  startAngle: 0,
                  endAngle: Math.PI * 2,
                },
                style: {
                  fill: fillColor,
                  stroke: '#FEF1D84D',
                  lineWidth: (params.dataIndex + 1) % groupSize === 0 ? unitLength : 0,
                },
              }
            },
          },
        ],
      }

      chart.setOption(option)
    },
  },
})
</script>

<template>
  <div v-loading="loading" style="display: flex;align-items: center; justify-content: center; gap: 120px; padding: 20px">
    <div ref="chartRef" style="width: 400px; height: 400px"></div>
    <aside>
      <section class="card">
        <header class="title">管道信息</header>
        <div class="item">
          <span>
            <img src="@/assets/onSiteView/dialog/length.svg" alt="" class="icon">
            <span>长度：</span>
          </span>
          <span>{{ data.parentData.parentData.length / 100 }}m</span>
        </div>

        <div class="item">
          <span>
            <img src="@/assets/onSiteView/dialog/area.svg" alt="" class="icon">
            <span>截面积：</span>
          </span>
          <span>{{ data.parentData.parentData.crossSectionalArea }}mm²</span>
        </div>

        <div class="item">
          <span>
            <img src="@/assets/onSiteView/dialog/current.svg" alt="" class="icon">
            <span>最大电流：</span>
          </span>
          <span>{{ data.parentData.parentData.maxCurrent }}A</span>
        </div>
      </section>

      <section class="card" style="margin-top: 16px">
        <header class="title">温度分层</header>
        <div v-for="item in groupList" :key="item.title" class="item">
          <div style="display: flex; align-items: center;gap: 6px">
            <div style="width: 14px;height: 14px;border-radius: 50%;" :style="{backgroundColor: item.color}" />
            <span>{{ item.title }}：</span>
          </div>
          <span>{{ item.temp }}℃</span>
        </div>

      </section>
    </aside>
  </div>
</template>

<style scoped lang="scss">
.card {
  width: 266px;
  display: flex;
  padding: 24px 20px;
  flex-direction: column;
  gap: 14px;
  border-radius: 10px;
  background: #F8F9FA;

  color: #606266;
  font-size: 14px;
  line-height: 22px;

  .title {
    font-size: 16px;
    color: #333;
    font-weight: bold;

  }

  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .icon {
      margin-right: 6px;
      width: 16px;
      height: 16px;
      vertical-align: middle
    }
  }

}
</style>
