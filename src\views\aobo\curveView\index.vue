<template>
  <div v-loading="loading" class="main-content">
    <el-tabs v-model="page" @tab-click="tabChange()">
      <el-tab-pane label="实时趋势" name="first" />
      <el-tab-pane label="单点曲线" name="second" />
    </el-tabs>
    <div class="top">
      <el-date-picker
        v-if="page === 'second'"
        v-model="date"
        style="margin-right: 20px; width: 200px"
        placeholder="选择日期"
        value-format="yyyy-MM-dd"
        :format="'yyyy-MM-dd'"
        append-to-body
        @change="dateChange"
      />

      <el-cascader
        v-if="page === 'first'"
        v-model="opticalFiberList"
        style="margin-right: 20px; width: 400px"
        :options="sensorList"
        collapse-tags
        clearable
        :props="{ value: 'id', label: 'name', children: 'childList', multiple: true }"
        @change="deviceCascaderChange()"
      />
      <el-cascader
        v-if="page === 'second'"
        v-model="idArray"
        style="margin-right: 20px; width: 330px"
        :options="sensorList"
        :props="{ value: 'id', label: 'name', children: 'childList' }"
        @change="cascaderChange"
      ></el-cascader>

      <VirtualSelect
        v-if="page === 'second'"
        v-model="monitoringPointId"
        placeholder="请选择"
        :list="monitoringPointList"
        label="name"
        value="id"
        filterable
        style="width: 100px; margin: 0 10px"
        @change="monitoringPointChange"
      />
      <template v-if="page === 'first'">
        <div style="margin-left: 20px; font-weight: bold">数据刷新间隔时间:</div>
        <el-select v-model="timeInterval" placeholder="请选择" filterable allow-create style="width: 100px; margin: 0 10px" @change="timeIntervalChange">
          <el-option v-for="item in timeListCharts" :key="item" :label="item" :value="item" />
        </el-select>
        <div style="font-weight: bold">秒</div>
        <!-- 实时/历史模式切换 -->
        <div v-if="systemInfo.singleLineRetainTime && getDataTime" class="mode-switch-inline">
          <el-radio-group v-model="viewMode" size="small" @change="viewModeChange">
            <el-radio-button label="realtime">实时</el-radio-button>
            <el-radio-button label="history">历史</el-radio-button>
          </el-radio-group>
          <!-- 历史模式时间轴 -->
          <template v-if="viewMode === 'history'">
            <span class="time-label">{{ formatTime(historyTimeRange[0]) }}</span>
            <el-slider
              v-model="currentHistoryTime"
              :min="historyTimeRange[0]"
              :max="historyTimeRange[1]"
              :step="300000"
              :show-tooltip="true"
              :format-tooltip="formatTime"
              style="width: 300px; margin: 0 15px"
              @change="getHistoryData"
            />
            <span class="time-label">{{ formatTime(historyTimeRange[1]) }}</span>
          </template>
        </div>
      </template>
    </div>
    <div class="line_chart">
      <div style="height: 35px; display: flex; align-items: flex-end; margin-bottom: 15px">
        <div class="title">{{ title }}</div>
        <div v-if="page === 'first'" class="time">
          <img src="@/assets/page/<EMAIL>" />
          <div>{{ `当前数据采集时间：${getDataTime || '---'}` }}</div>
        </div>
        <div v-if="page === 'second'" class="time">
          <img src="@/assets/page/weizhi.png" style="width: 18px; height: 18px" />
          <div>{{ `当前位置：${positionName}` }}</div>
        </div>
      </div>
      <div v-if="page === 'first'">
        <line-charts
          v-if="chartsData.xAxisData && chartsData.xAxisData.length > 0"
          ref="chart"
          :page="page"
          :zoom-start="zoomStart"
          :zoom-end="zoomEnd"
          :charts-selected="chartsSelected"
          @zoomChange="zoomChange"
          @legendselectchanged="legendselectchanged"
        />
        <div v-if="!chartsData.xAxisData.length" class="no_data">
          <img src="@/assets/page/yypz_icon_zanwu.png" />
          <div>暂无数据</div>
        </div>
      </div>

      <div v-if="page === 'second'">
        <line-charts-point
          v-if="singleChartsData.xAxisData.length && singleChartsData.xAxisData.length > 0"
          ref="chartPoint"
          :prop-data="singleChartsData"
          :page="page"
        />
        <div v-if="!singleChartsData.xAxisData.length" class="no_data">
          <img src="@/assets/page/yypz_icon_zanwu.png" />
          <div>暂无数据</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import { liveTrend, singleLine } from '@/api/lineCharts'
import * as echarts from 'echarts'

import { getAlarmLevelList } from '@/api/alarmTactics'
import { cableOptions, monitoringPoint } from '@/api/deviceManage'
import VirtualSelect from '@/components/VirtualSelect/Select'
import lineCharts from './modules/lineCharts'
import lineChartsPoint from './modules/lineChartsPoint'

const alarmColorMap = {
  0: '#0000FF',
  1: '#FFA500',
  2: '#e4e46f',
  3: '#FF0000',
}

export default {
  name: 'CurveView',
  components: {
    lineCharts,
    lineChartsPoint,
    VirtualSelect,
  },
  data() {
    return {
      page: 'first',
      title: '实时温度趋势',
      getDataTime: null, // 当前数据采集时间
      opticalFiberList: null, // 选中光纤数组
      idArray: [],
      date: dayjs().format('YYYY-MM-DD'),
      colorList: [
        { color1: '#5470c6', color2: 'rgba(84, 112, 198, 0)' },
        { color1: '#91cc75', color2: 'rgba(145, 204, 117, 0)' },
        { color1: '#fac858', color2: 'rgba(250, 200, 88, 0)' },
        { color1: '#ee6666', color2: 'rgba(238, 102, 102, 0)' },
        { color1: '#73c0de', color2: 'rgba(115, 192, 222, 0)' },
        { color1: '#3ba272', color2: 'rgba(59, 162, 114, 0)' },
        { color1: '#fc8452', color2: 'rgba(252, 132, 82, 0)' },
        { color1: '#9a60b4', color2: 'rgba(154, 96, 180, 0)' },
        { color1: '#ea7ccc', color2: 'rgba(234, 124, 204, 0)' },
      ],
      chartsData: {
        xAxisData: [],
        seriesData: [],
      },
      singleChartsData: {
        xAxisData: [],
        seriesData: [],
      },
      lineChartData: null,
      loading: false,
      positionName: '', // 单点曲线当前电池位置
      zoomStart: 0,
      zoomEnd: 100,
      interval: null,
      timeListCharts: [10, 30, 60], // 间隔时间下拉框
      timeInterval: Number(localStorage.getItem('timeInterval')) || 60,
      chartsSelected: {},
      firstLoad: true, // 是否是初次加载

      sensorList: [],
      alarmLevelList: [],
      monitoringPointList: [],
      monitoringPointId: null,
      // 视图模式相关
      viewMode: 'realtime', // 'realtime' 或 'history'
      historyTimeRange: [], // 历史时间范围 [开始时间戳, 结束时间戳]
      currentHistoryTime: null, // 当前选中的历史时间
    }
  },
  computed: {
    ...mapGetters(['tableHeaderStyle', 'btnAuthority', 'systemInfo']),
  },
  created() {
    // 获取路由传参
    const { id } = this.$route.query
    if (id) {
      this.page = 'second'
      this.spaceId = Number(id)
      this.tabChange()
    }
    this.getAlarmLevelList()
  },

  beforeDestroy() {
    if (this.interval) {
      clearInterval(this.interval)
    }
  },
  async mounted() {
    this.cableDropdownTime()
  },
  methods: {
    // 选择数据刷新间隔时间
    timeIntervalChange() {
      localStorage.setItem('timeInterval', this.timeInterval)
      if (this.interval) {
        clearInterval(this.interval)
        this.interval = setInterval(() => {
          this.getChartsData()
        }, Number(this.timeInterval) * 1000)
      }
    },
    /**
     * 获取等级列表
     * */
    getAlarmLevelList() {
      getAlarmLevelList().then((res) => {
        this.alarmLevelList = res.data
        // 根据value排序，大的在前
        this.tempLevelList = res.data[0].levels.sort((a, b) => b.value - a.value)
      })
    },

    /**
     * 根据温度判断报警等级
     * */
    getAlarmLevel(temp) {
      if (!this.tempLevelList.length) return
      for (const item of this.tempLevelList) {
        if (temp >= item.value) {
          return item.level
        }
      }
    },

    tabChange() {
      this.title = this.page === 'first' ? '实时温度趋势' : '温度趋势'
      this.firstLoad = true
      this.loading = true
      this.date = dayjs().format('YYYY-MM-DD')
      // 重置视图模式为实时
      this.viewMode = 'realtime'
      if (this.interval) {
        clearInterval(this.interval)
        if (this.page === 'first') {
          this.interval = setInterval(() => {
            this.getChartsData()
          }, Number(this.timeInterval) * 1000)
        }
      }
      this.chartsData.xAxisData = []
      this.singleChartsData.xAxisData = []
      this.lineChartData = []
      // 提取查找第一个包含子项的元素逻辑，确保级联选择框有默认值
      const firstItemWithChild = this.sensorList.find((item) => item.childList && item.childList.length > 0)
      let firstItemWithArray = []
      if (firstItemWithChild) {
        firstItemWithArray = [firstItemWithChild.id, firstItemWithChild.childList[0].id]
      }

      if (this.page === 'first') {
        this.idArray = []
        this.spaceId = null
        this.cableDropdownTime()
      } else {
        // 单点曲线页面
        // this.idArray = firstItemWithArray
        this.opticalFiberList = null
        // 如果设置了默认值，触发级联选择变化事件
        this.$nextTick(() => {
          if (firstItemWithArray.length > 0) {
            this.idArray = firstItemWithArray || []
            this.cascaderChange(1)
          }
        })
      }
    },
    // 电缆下拉（实时专用）
    cableDropdownTime() {
      clearInterval(this.interval)
      cableOptions({ withChild: true }).then((res) => {
        if (res.data && res.data.length > 0) {
          res.data.forEach((item) => {
            if (!Array.isArray(item.childList)) {
              this.$set(item, 'childList', [])
            }
          })
        }
        this.sensorList = res.data
        const firstItemWithChild = this.sensorList.find((item) => item.childList && item.childList.length > 0)
        if (firstItemWithChild) {
          this.opticalFiberList = [[firstItemWithChild.id, firstItemWithChild.childList[0].id]]
        }
        this.getChartsData()
        this.interval = setInterval(() => {
          this.getChartsData()
        }, Number(this.timeInterval) * 1000)
      })
    },

    // 根据电缆获取监控段的所有点位
    getMonitoringPoint(type) {
      if (!this.idArray || !this.idArray.length) {
        this.loading = false
        return
      }
      const id = this.idArray[this.idArray.length - 1]
      monitoringPoint(id)
        .then((res) => {
          this.monitoringPointList = res.data
          if (type === 1 && res.data.length) {
            this.monitoringPointId = this.monitoringPointList[0].id
            this.getTempPointData()
          } else {
            this.loading = false
            this.singleChartsData = {
              xAxisData: [],
              seriesData: [],
            }
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 设备级联下拉框
    deviceCascaderChange(val) {
      this.firstLoad = true
      this.getChartsData()
    },

    // 单点电缆下拉改变
    cascaderChange(type) {
      this.firstLoad = true
      // 设置当前位置
      this.positionName = ''

      this.getMonitoringPoint(1)
    },
    monitoringPointChange() {
      if (this.monitoringPointId) {
        this.getChartsDataPoint()
      }
    },
    // 图例选中改变
    legendselectchanged(e) {
      this.chartsSelected = e.chartsSelected
    },
    // 缩放比列改变
    zoomChange(e) {
      this.zoomStart = e.zoomStart
      this.zoomEnd = e.zoomEnd
    },
    dateChange() {
      this.firstLoad = true
      this.getChartsData()
    },
    // 获取实时趋势数据
    getChartsData() {
      if (this.page === 'second') {
        this.getChartsDataPoint()
        return
      }

      if (this.firstLoad) {
        this.firstLoad = false
        // this.loading = true
      }

      // 根据当前视图模式决定是否获取历史数据
      const isHistory = this.viewMode === 'history'
      this.getTempRealData(isHistory)
    },
    /**
     * 获取温度实时/历史趋势数据
     * @param {boolean} isHistory - 是否获取历史数据
     * */
    getTempRealData(isHistory = false) {
      if (!this.opticalFiberList) return
      const data = {
        segmentIds: Array.from(new Set(this.opticalFiberList.map((item) => item[1]))),
      }

      // 如果是历史模式，添加time字段
      if (isHistory && this.currentHistoryTime) {
        data.time = dayjs(this.currentHistoryTime).format('YYYY-MM-DD HH:mm:ss')
      }

      this.loading = true
      liveTrend(data)
        .then((res) => {
          this.chartsData = {
            xAxisData: [],
            seriesData: [],
          }
          this.lineChartData = []
          if (!res.data || !res.data.segments.length) {
            return
          }
          const { segments } = res.data

          this.getDataTime = res.data.refreshTime
          // 使用数组展开运算符和 concat 方法一次性合并数据，提升性能
          this.lineChartData = this.lineChartData.concat(...segments)
          // 判断哪一项有数据,如果都没有temperatures数据，则为暂无数据
          const haveDataArray = this.lineChartData.find((item) => (item.temperatures || []).length)
          if (haveDataArray) {
            this.chartsData.xAxisData = haveDataArray.temperatures.map((item) => item.position)
          } else {
            this.lineChartData = []

            return
          }
          this.lineChartData.forEach((item) => {
            item.temperatures.forEach((item1) => {
              item1.fiberId = item.fiberId
              item1.startPosition = item.startPosition
            })
          })
          this.chartsData.seriesData = this.lineChartData.map((item, index) => {
            const datas = (item.temperatures || []).map((item1) => {
              this.$set(item1, 'itemStyle', {
                color: alarmColorMap[this.getAlarmLevel(item1.value)] || '#00E667',
              })

              this.$set(item1, 'itemStyle', {
                color: alarmColorMap[this.getAlarmLevel(item1.value)] || '#00E667',
                opacity: this.getAlarmLevel(item1.value) !== undefined ? 1 : 0,
              })
              item1.value = item1.temperature
              return [item1.position, (item1.value / 100).toFixed(2), item1]
            })
            const that = this
            return {
              name: `${item.name}`,
              type: 'line',
              data: datas,
              smooth: true, // 平滑曲线
              sampling: 'lttb',
              large: true,
              symbolSize(params) {
                return that.getAlarmLevel(params[1]) !== undefined ? 8 : 0
              },
              itemStyle: {
                normal: {
                  color(params) {
                    // params 是当前数据点的信息
                    return alarmColorMap[that.getAlarmLevel(params.value[1])] || that.colorList[index].color1
                  },
                },
              },
              lineStyle: { width: 2.5, color: this.colorList[index].color1 },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: this.colorList[index].color2 },
                  { offset: 0.9, color: '#fff' },
                ]),
              },
              label: {
                show: false,
                position: 'top',
                formatter: (val) => `${(val.value / 100).toFixed(2)}℃`,
              },
            }
          })
          setTimeout(() => {
            this.$refs.chart?.updateOption(this.chartsData.seriesData, true)
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 获取单点曲线数据
    getChartsDataPoint() {
      this.getTempPointData()
    },
    /**
     * 获取温度单点曲线数据
     * */
    getTempPointData() {
      if (!this.idArray) return
      this.lineChartData = []

      this.page = 'second'
      const datas = {
        segmentId: this.idArray[this.idArray.length - 1],
        date: this.date,
        position: this.monitoringPointId,
      }
      if (this.firstLoad) {
        this.firstLoad = false
        this.loading = true
      }
      singleLine(datas)
        .then((res) => {
          this.positionName = res.data.cableStr + res.data.segmentStr

          this.singleChartsData = {
            xAxisData: [],
            seriesData: [],
          }
          this.$nextTick(() => {
            this.lineChartData = res.data.temperatures || []
            const tempSet = new Set()
            for (const item of this.lineChartData) {
              tempSet.add(item.time)
            }
            this.singleChartsData.xAxisData = [...tempSet]
            if (!this.singleChartsData.xAxisData.length) return
            this.singleChartsData.seriesData = [
              {
                // name: el.name,
                type: 'line',
                data: res.data.temperatures.map((item) => {
                  item.value = (item.temperature / 100).toFixed(2)
                  return item
                }),
                smooth: true, // 平滑曲线
                symbolSize: 8,
                showAllSymbol: false,
                sampling: 'average',
                large: true,
                // showSymbol: true,
                symbol: 'none',
                // itemStyle: { color: '#1768EB' },
                // lineStyle: { width: 2.5, color: '#1768EB' },
                lineStyle: { width: 2.5 },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(48, 149, 251, 0.4)' },
                    { offset: 0.9, color: '#fff' },
                  ]),
                },
                label: {
                  show: false,
                  position: 'top',
                  formatter: (val) => `${(val.value / 100).toFixed(2)}℃`,
                },
              },
            ]
            for (const item of this.tempLevelList) {
              /** 阈值线*/
              this.singleChartsData.seriesData.push({
                name: '',
                type: 'line',
                data: [item.value],
                symbol: 'none',
                label: {
                  show: false,
                },
                markLine: {
                  silent: true,
                  data: [
                    {
                      name: `阈值线：${item.value}℃`,
                      yAxis: item.value,
                      label: {
                        formatter: '{b}',
                        position: 'end',
                        color: alarmColorMap[item.level],
                      },
                    },
                  ],
                  lineStyle: {
                    color: alarmColorMap[item.level],
                    width: 2,
                  },
                  symbol: 'none',
                  label: {
                    distance: [20, 8],
                  },
                },
              })
            }
          })
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * 初始化历史时间范围（当前时间减去数据保存时间）
     */
    initHistoryTimeRange() {
      const now = new Date(this.getDataTime).getTime()
      const sevenDaysAgo = now - this.systemInfo.singleLineRetainTime * 60 * 60 * 1000
      this.historyTimeRange = [sevenDaysAgo, now]
      this.currentHistoryTime = now // 默认选中当前时间
    },

    /**
     * 视图模式切换
     */
    viewModeChange() {
      if (this.viewMode === 'realtime') {
        // 切换到实时模式
        this.getChartsData()
        // 启动定时器
        if (this.interval) {
          clearInterval(this.interval)
        }
        this.interval = setInterval(() => {
          this.getChartsData()
        }, Number(this.timeInterval) * 1000)
      } else {
        this.initHistoryTimeRange()

        // 切换到历史模式
        // 停止定时器
        if (this.interval) {
          clearInterval(this.interval)
        }
        // 获取历史数据
        this.getHistoryData()
      }
    },

    /**
     * 格式化时间戳为可读格式
     */
    formatTime(timestamp) {
      return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
    },

    /**
     * 获取历史数据
     */
    getHistoryData() {
      // 根据 currentHistoryTime 获取对应时间点的历史数据
      this.getTempRealData(true) // 传入true表示获取历史数据
    }
  },
}
</script>

<style lang="scss" scoped>
.main-content {
  height: calc(100%);
  box-sizing: border-box;
  font-family: PingFang SC RE;
  will-change: transform;

  .mode-switch-inline {
    display: flex;
    align-items: center;
    margin-left: 30px;
    gap: 10px;

    .time-label {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
      min-width: 80px;
      text-align: center;
    }
  }
  .top {
    display: flex;
    align-items: center;
    margin: 10px 0;
  }
  .line_chart {
    width: 100%;
    // height: 100vh;
    position: relative;
    .time {
      display: flex;
      align-items: center;
      img {
        width: 15px;
        height: 15px;
        margin-right: 5px;
      }
      div {
        font-family: PingFang SC RE;
        color: #737377;
        font-size: 14px;
        font-weight: bold;
        display: flex;
      }
    }
    .title {
      font-size: 18px;
      font-family: PingFang SC RE;
      font-weight: bold;
      color: #202225;
      margin-right: 50px;
      padding-left: 10px;
      border-left: 4px solid #1768eb;
    }
    .no_data {
      position: fixed;
      top: 90px;
      right: 50px;
      left: 100px;
      bottom: 20px;
      display: grid;
      justify-content: center;
      place-content: center;
      place-items: center;
      z-index: -1;
      img {
        width: 250px;
        height: 200px;
      }
      div {
        font-size: 20px;
        font-weight: bold;
        color: #7d7d7d;
      }
    }
  }
}
</style>
<style lang="scss">
.el-cascader-panel {
  .el-cascader-menu:nth-child(1) {
    li {
      .el-checkbox {
        display: none;
      }
    }
  }
}
</style>
