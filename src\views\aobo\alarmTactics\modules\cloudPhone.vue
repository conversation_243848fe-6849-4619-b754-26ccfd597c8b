<template>
  <div>
    <div style="display:flex;justify-content: space-between">
      <el-input
        v-model="keyword"
        placeholder="请输入搜素内容"
        suffix-icon="el-icon-search"
        style="width:290px;margin-right:20px;"
        @keyup.enter.native="getList(1)"
        @blur="getList(1)"
      />
      <div>
        <el-button
          type="primary"
          @click="add"
        >新增
        </el-button>
        <el-button
          type="success"
          @click="notificationRecords"
        >通知记录
        </el-button>
      </div>
    </div>
    <!--    表格-->
    <div style="margin-top: 20px">
      <el-table
        v-loading="loading"
        :header-cell-style="tableHeaderStyle"
        header-row-class-name="table-header"
        :data="tableData"
        height="60vh"
        stripe
        row-key="id"
      >
        <el-table-column
          label="序号"
          type="index"
          width="100"
          align="center"
        />
        <el-table-column
          prop="nickName"
          label="告警电缆"
          show-overflow-tooltip
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.spaceNames ? scope.row.spaceNames.join(', ') : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="username"
          label="通知人员"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.personNames ? scope.row.personNames.join(', ') : '--' }}</span>          </template>
        </el-table-column>

        <el-table-column
          prop="state"
          label="状态"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-switch
              :value="scope.row.state"
              :active-value="0"
              :inactive-value="1"
              @change="changeState(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="sex"
          label="最后更新时间"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.updateTime || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          align="center"
        >
          <template v-slot="scope">
            <el-button
              type="text"
              style="color: #1768EB"
              @click="detail(scope.row)"
            >详情
            </el-button>
            <el-button
              type="text"
              style="color: #02C69E"
              @click="edit(scope.row)"
            >编辑
            </el-button>
            <el-button
              type="text"
              style="color: #FF4242"
              @click="del(scope.row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page.sync="pageNum"
        :page-size="pageSize"
        layout="total,prev, pager, next,sizes, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 新增弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="35vw"
      close-on-click-modal
      destroy-on-close
      @close="closeDialog()"
    >
      <div
        v-if="dialogVisible"
        style="padding-left:32px"
      >
        <el-form
          ref="formNew"
          :model="form"
          label-width="3.4rem"
          :rules="rules"
        >
          <el-form-item
            label="告警电缆："
            prop="spaceIds"
          >
            <el-select
              v-model="form.spaceIds"
              multiple
              collapse-tags
              placeholder="请选择"
              style="width:250px;"
            >
              <el-option
                v-for="item in batteryRoomOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="通知频率："
            prop="frequency"
          >
            同一个告警点
            <el-input-number
              v-model="form.frequency"
              placeholder="请输入"
              :controls="false"
              :min="1"
              style="width: 80px;"
            />
            分钟，发起一次电话通知。
          </el-form-item>
          <el-form-item label="状态">
            <el-switch
              v-model="form.state"
              :active-value="0"
              :inactive-value="1"
            />
          </el-form-item>
        </el-form>
        <div class="line" />
        <div style="margin-top: 24px">
          <el-button
            type="primary"
            plain
            @click="addNewList()"
          >新 增
          </el-button>
        </div>
        <div style="border-radius: 4px;border: #E0E0E0 solid 1px;margin-top: 20px">
          <div style="display: flex;width: 100%;background: #F7F8F9;">
            <div
              class="itemBox"
              style="width: 40%"
            >
              通知时间段
            </div>
            <div
              class="itemBox"
              style="width: 40%"
            >
              通知人员
            </div>
            <div
              class="itemBox"
              style="width: 20%"
            >
              操作
            </div>
          </div>
          <div
            v-for="(item, index) in form.personnelInformation"
            :key="item.personId"
            style="display:flex;"
          >
            <div
              class="itemBox"
              style="width: 40%"
            >
              <el-time-picker
                v-model="item.time"
                style="width: 240px;"
                is-range
                :clearable="false"
                arrow-control
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                value-format="HH:mm"
                :editable="false"
                format="HH:mm"
                @change="timeChange(item.time, item)"
              />
            </div>
            <div
              class="itemBox"
              style="width: 40%"
            >
              <el-select
                v-model="item.personId"
                placeholder="请选择"
              >
                <el-option
                  v-for="person in personnelOptions"
                  :key="person.id"
                  :label="person.name"
                  :value="person.id"
                />
              </el-select>
            </div>
            <div
              class="itemBox"
              style="width: 20%"
            >
              <el-button
                type="text"
                style="color: red"
                :disabled="index === 0"
                @click="form.personnelInformation.splice(index, 1)"
              >删除</el-button>
            </div>
          </div>
        </div>

      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button
          type="primary"
          plain
          style="margin-right:10px"
          @click="closeDialog()"
        >取 消
        </el-button>
        <el-button
          type="primary"
          @click="submitNew()"
        >确 定
        </el-button>
      </div>
    </el-dialog>
    <!-- 删除弹窗-->
    <el-dialog
      title="删除"
      :visible.sync="delDialogVisible"
      width="30%"
    >
      <span>是否确定删除</span>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="delDialogVisible = false, delId = -1">取 消</el-button>
        <el-button
          type="primary"
          @click="doDelete"
        >确 定</el-button>
      </span>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog
      title="详情"
      :visible.sync="detailVisible"
      width="35vw"
      close-on-click-modal
      destroy-on-close
      @close="closeDetail"
    >
      <div
        v-if="detailVisible"
        style="padding-left:32px"
      >
        <el-form
          ref="formNew"
          disabled
          :model="form"
          label-width="3.4rem"
        >
          <el-form-item
            label="告警电缆："
            prop="spaceIds"
          >
            <el-select
              v-model="form.spaceIds"
              multiple
              collapse-tags
              placeholder="请选择"
              style="width:250px;"
            >
              <el-option
                v-for="item in batteryRoomOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="通知频率："
            prop="frequency"
          >
            同一个告警点
            <el-input-number
              v-model="form.frequency"
              placeholder="请输入"
              :controls="false"
              :min="1"
              style="width: 80px;"
            />
            分钟，发起一次电话通知。
          </el-form-item>
          <el-form-item label="状态">
            <el-switch
              v-model="form.state"
              :active-value="0"
              :inactive-value="1"
            />
          </el-form-item>
        </el-form>
        <div class="line" />
        <div style="border-radius: 4px;border: #E0E0E0 solid 1px;margin-top: 20px">
          <div style="display: flex;width: 100%;background: #F7F8F9;">
            <div class="itemBox">
              通知时间段
            </div>
            <div class="itemBox">
              通知人员
            </div>
          </div>
          <div
            v-for="item in form.personnelInformation"
            :key="item.personId"
            style="display:flex;"
          >
            <div class="itemBox">
              <span>{{ item.startTime }}</span>
              <span>~</span>
              <span>{{ item.endTime }}</span>
            </div>
            <div class="itemBox">
              <el-select
                v-model="item.personId"
                disabled
                placeholder="请选择"
              >
                <el-option
                  v-for="person in personnelOptions"
                  :key="person.id"
                  :label="person.name"
                  :value="person.id"
                />
              </el-select>
            </div>
          </div>
        </div>

      </div>
    </el-dialog>

  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  changeState, doDelete, doDropPerson, doDropSpace, doPage, doSave, doUpdate
} from '@/api/aobo/cloudPhone'

export default {
  name: 'BaseConfig',
  data() {
    return {
      delId: -1,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      batteryRoomOptions: [], // 煤矿下拉框
      personnelOptions: [], // 人员下拉框
      personIdMap: {},
      rules: {
        spaceIds: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        frequency: [
          { required: true, message: '请输入频率', trigger: 'change' }
        ]
      },
      newRules: {
        spaceIds: [
          { required: true, message: '请选择角色名称', trigger: 'change' }
        ]
      },
      form: {
        id: '',
        spaceIds: [],
        frequency: 0,
        state: 0,
        personnelInformation: [{
          time: '',
          // time: [dayjs().startOf('day'), dayjs().endOf('day')],
          personId: null
        }]
      }, // 新增弹窗数据
      dialogVisible: false,
      delDialogVisible: false,
      tableData: [], // 列表数据
      loading: false,
      keyword: '',
      title: '',
      detailVisible: false
    }
  },
  computed: {
    ...mapGetters([
      'tableHeaderStyle', 'btnAuthority'
    ])
  },
  created() {
    this.getList()
    this.doDropSpace()
    this.doDropPerson()
  },
  methods: {
    timeChange(timeList, data) {
      const [startTime, endTime] = timeList
      data.startTime = startTime
      data.endTime = endTime
    },
    changeState(data) {
      changeState(data.id).then((res) => {
        this.$message.success('成功')
        this.getList()
      }).catch(() => {
        this.$message.warning('失败')
        this.getList()
      })
    },
    addNewList() {
      this.form.personnelInformation.push({
        time: '',
        personId: null
      })
    },
    doDropSpace() {
      doDropSpace().then((res) => {
        this.batteryRoomOptions = res.data
      })
    },
    doDropPerson() {
      doDropPerson().then((res) => {
        this.personnelOptions = res.data
        for (const person of this.personnelOptions) {
          this.personIdMap[person.id] = true
        }
      })
    },
    closeDialog() {
      this.dialogVisible = false
      this.$refs.formNew.resetFields()
      this.$refs.formNew.clearValidate()
    },
    closeDetail() {
      this.detailVisible = false
      this.$refs.formNew.resetFields()
      this.$refs.formNew.clearValidate()
    },
    submitNew() {
      this.$refs.formNew.validate((valid) => {
        if (valid) {
          const { id } = this.form
          const fn = id ? doUpdate : doSave
          let isValidate = false
          this.form.personnelInformation.forEach((item) => {
            console.log('1111', item.time)
            if (item.time) {
              [item.startTime, item.endTime] = item.time
            } else if (!item.time && item.personId) {
              isValidate = true
            }
          })
          if (isValidate) {
            this.$message.warning('人员通知时间未填写')
            return
          }
          fn(this.form).then((res) => {
            this.$message.success('成功')
            this.getList(1)
            this.closeDialog()
          })
        }
      })
    },

    notificationRecords() {
      this.$emit('showRecords')
    },
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
        this.selectList = []
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {},
        keyword: this.keyword
      }
      this.loading = true
      doPage(params).then((res) => {
        this.tableData = res.data.records.map((item) => item)
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      }).finally(() => {
        this.loading = false
      })
    },
    add() {
      this.form = {
        id: '',
        spaceIds: [],
        state: 0,
        personnelInformation: [{
          time: '',
          personId: null
        }]
      }
      this.title = '新增'
      this.dialogVisible = true
    },
    detail(row) {
      this.form = { ...row }
      this.detailVisible = true
    },
    edit(row) {
      this.title = '编辑'
      this.form = { ...row }
      this.form.personnelInformation = row.personnelInformation.map((item) => ({
        personId: this.personIdMap[item.personId] ? item.personId : null,
        time: item.startTime ? [item.startTime, item.endTime] : null
      }))
      this.dialogVisible = true
    },
    del(row) {
      this.delId = row.id
      this.delDialogVisible = true
    },
    doDelete() {
      doDelete(this.delId).then((res) => {
        this.$message.success('成功')
        this.getList()
      })
      this.delDialogVisible = false
    },

    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
.line {
  width: 100%;
  height: 1px;
  background: #E5E5E5;
}
.itemBox {
  width: 50%;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: #E0E0E0 solid 1px
}
</style>
