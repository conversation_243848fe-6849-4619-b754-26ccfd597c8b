import { OutputPass } from 'three/examples/jsm/postprocessing/OutputPass'
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass'
import * as THREE from 'three'
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass'
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader'
import { camera, canvasSize, renderer, scene } from '@/views/aobo/fieldView/threeJs/service/core'

import { BlendFunction, SelectiveBloomEffect, EffectComposer, EffectPass, RenderPass, OutlineEffect } from 'postprocessing'

export class PostProcess {
  outlinePass

  bloomPass

  composer

  initialized = false

  // bloom 参数
  bloomParams = {
    intensity: 3.0,
    radius: 0.85,
    luminanceThreshold: 0.2,
    luminanceSmoothing: 0.2,
    inverted: false,
    ignoreBackground: false,
    opacity: 1.0,
    // blendFunction: 26,
    blendFunction: BlendFunction.REFLECT,
  }

  // outline 参数
  outlineParams = {
    edgeStrength: 10,
    pulseSpeed: 0.63,
    visibleEdgeColor: 'red',
    hiddenEdgeColor: 'red',
    blur: true,
    xRay: true,
    patternScale: 53,
    multisampling: 4,
    opacity: 1.0,
    blendFunction: BlendFunction.ADD,
  }

  constructor() {}

  init() {
    if (this.initialized) return
    this.initialized = true
    const composer = new EffectComposer(renderer)
    this.composer = composer
    const renderPass = new RenderPass(scene, camera)
    composer.addPass(renderPass)

    /** 重要，避免后处理影响原场景的显示*/
    // const outputPass = new OutputPass()
    // composer.addPass(outputPass)

    /** 辉光*/
    const bloomPass = new SelectiveBloomEffect(scene, camera, {
      blendFunction: this.bloomParams.blendFunction,
      mipmapBlur: true,
      luminanceThreshold: this.bloomParams.luminanceThreshold,
      luminanceSmoothing: this.bloomParams.luminanceSmoothing,
      intensity: this.bloomParams.intensity,
      radius: this.bloomParams.radius,
    })

    // 设置 SelectiveBloomEffect 特有的属性
    bloomPass.inverted = this.bloomParams.inverted
    bloomPass.ignoreBackground = this.bloomParams.ignoreBackground

    this.bloomPass = bloomPass

    composer.addPass(new EffectPass(camera, bloomPass))

    /** 边框效果*/
    const outlineEffect = new OutlineEffect(scene, camera, {
      blendFunction: this.outlineParams.blendFunction,
      multisampling: Math.min(this.outlineParams.multisampling, renderer.capabilities.maxSamples),
      edgeStrength: this.outlineParams.edgeStrength,
      pulseSpeed: this.outlineParams.pulseSpeed,
      visibleEdgeColor: this.outlineParams.visibleEdgeColor,
      hiddenEdgeColor: this.outlineParams.hiddenEdgeColor,
      height: 480,
      blur: this.outlineParams.blur,
      xRay: this.outlineParams.xRay,
    })

    this.outlinePass = outlineEffect
    composer.addPass(new EffectPass(camera, outlineEffect))
    // composer.addPass(new EffectPass(camera, bloomPass))
    // const outlinePass = new OutlinePass(new THREE.Vector2(canvasSize.width, canvasSize.height), scene, camera)
    // this.outlinePass = outlinePass
    // this.resetOutlinePass()
    // outlinePass.visibleEdgeColor.set('#5176A9') // 包围线颜色
    // outlinePass.hiddenEdgeColor.set('green') // 被遮挡的边界线颜色
    // composer.addPass(outlinePass)

    /** 抗锯齿*/
    // const effectFXAA = new ShaderPass(FXAAShader)
    // const pixelRatio = renderer.getPixelRatio()
    // effectFXAA.uniforms.resolution.value.set(
    //   1 / (canvasSize.width * pixelRatio),
    //   2 / (canvasSize.height * pixelRatio)
    // )
    // effectFXAA.renderToScreen = true
    // effectFXAA.needsSwap = true
    // composer.addPass(effectFXAA)
  }

  /** 重置边框效果*/
  resetOutlinePass() {
    const { outlinePass } = this
    outlinePass.edgeStrength = 5 // 包围线浓度
    outlinePass.edgeGlow = 4 // 边缘线范围
    outlinePass.edgeThickness = 10 // 边缘线浓度
    outlinePass.pulsePeriod = 10 // 包围线闪烁频率
  }
}
