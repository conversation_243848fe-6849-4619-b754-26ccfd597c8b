<template>
  <div>
    <div class="top">
      <div class="left">
        <img
          src="@/assets/device_manage/<EMAIL>"
          @click="back"
        >
        <div class="pa_name">DTS管理</div>
        <div class="ch_name">/</div>
        <div class="ch_name">{{ targetData.title }}</div>
      </div>
    </div>
    <div class="base_info">
      <div class="title">基础信息</div>
      <div class="content">
        <el-form
          ref="form1"
          :model="formData.baseInfo"
          :rules="rules"
          label-width="120px"
          class="form_part"
        >
          <el-form-item
            label="DTS主机信息:"
            prop="name"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.name"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.isEdit"
            />
          </el-form-item>
          <el-form-item
            label="mac地址:"
            prop="mac"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.mac"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.isEdit"
            />
          </el-form-item>
          <el-form-item
            label="采样率:"
            prop="tempResolutionRatio"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.tempResolutionRatio"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.isEdit || !isResolutionEdit"
            />
            <span>m</span>
          </el-form-item>
          <el-form-item
            label="刷新时间:"
            prop="refreshInterval"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.refreshInterval"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.isEdit"
            />
            <span>s</span>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="base_info">
      <div class="title">光缆信息</div>
      <div class="content">
        <el-form
          ref="form2"
          :model="formData.baseInfo"
          :rules="rules"
          label-width="120px"
          class="form_part"
        >
          <el-form-item
            label="光缆长度:"
            prop="lineLength"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.lineLength"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.isEdit"
            />
            <span>m</span>
          </el-form-item>
          <el-form-item
            label="纤芯类型:"
            prop="fibreCoreSize"
            class="form_item"
          >
            <el-input
              v-model="formData.baseInfo.fibreCoreSize"
              placeholder="请输入"
              size="large"
              style="width:250px;"
              :disabled="targetData.isEdit"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div
      v-if="!targetData.isEdit"
      class="center_btn"
    >
      <el-button
        type="primary"
        @click="saveBaseInfo"
      >确定</el-button>
    </div>
    <div class="table_list">
      <div class="title">分段设置</div>
      <div class="content">
        <div
          v-if="!targetData.isEdit"
          class="list_item"
        >
          <div class="item_info">
            <div class="input_style">
              <div class="text">起始点：</div>
              <el-input
                v-model="startPosition"
                placeholder="请输入"
                size="large"
                style="width:250px;"
              />
              <div class="text">m</div>
            </div>
            <div class="input_style">
              <div class="text">终点：</div>
              <el-input
                v-model="endPosition"
                placeholder="请输入"
                size="large"
                style="width:250px;"
              />
              <div class="text">m</div>
            </div>
            <!-- <div class="input_style">
              <div class="text">初始值：</div>
              <el-input
                v-model="defaultVal"
                placeholder="请输入"
                size="large"
                style="width:250px;"
              />
              <div class="text">℃</div>
            </div> -->
          </div>
          <div class="btn_list">
            <el-button
              type="primary"
              @click="addParagraph"
            >新增</el-button>
            <el-button
              type="danger"
              @click="clearParagraph"
            >清空分段</el-button>
            <el-button
              type="success"
              @click="bulkImport"
            >批量导入</el-button>
          </div>
        </div>
        <el-table
          ref="multipleTable"
          :header-cell-style="tableHeaderStyle"
          header-row-class-name="table-header"
          :data="tableData"
          stripe
          :height="targetData.isEdit ? 400 : 280"
        >
          <el-table-column
            label="监控段"
            type="index"
            width="70"
            align="center"
          />
          <el-table-column
            prop="startPosition"
            label="起始点（m）"
            show-overflow-tooltip
            align="center"
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.edit"
                v-model="scope.row.startPosition"
                placeholder="请输入"
                style="width:200px;"
                @input="calcParagraph(scope.row, scope.$index)"
              />
              <span v-else>{{ (scope.row.startPosition || scope.row.startPosition === 0)
                ? scope.row.startPosition : '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="endPosition"
            label="终点（m）"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.edit"
                v-model="scope.row.endPosition"
                placeholder="请输入"
                style="width:200px;"
                @input="calcParagraph(scope.row, scope.$index)"
              />
              <span v-else>{{ (scope.row.endPosition || scope.row.endPosition === 0)
                ? scope.row.endPosition : '--' }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="defaultVal"
            label="初始值（℃）"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.edit"
                v-model="scope.row.defaultVal"
                placeholder="请输入"
                style="width:200px;"
              />
              <span v-else>{{ scope.row.defaultVal || '--' }}</span>
            </template>
          </el-table-column> -->
          <el-table-column
            prop="pointCount"
            label="点位数"
            align="center"
            show-overflow-tooltip
          >
            <template slot="header">
              <div class="table_title">
                <div>点位数</div>
                <el-tooltip
                  effect="dark"
                  content="点位数=（终点-起始点）/采样率"
                  placement="top"
                >
                  <div class="tips">?</div>
                </el-tooltip>
              </div>
            </template>
            <template slot-scope="scope">
              <span>
                <span>{{ scope.row.pointCount || '--' }}</span>
              </span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!targetData.isEdit"
            label="操作"
            fixed="right"
            align="center"
            width="160"
          >
            <template v-slot="scope">
              <el-button
                v-if="scope.row.edit"
                type="text"
                style="text-decoration:underline;margin-right:15px;"
                @click="saveParagraph(scope.row)"
              >确定</el-button>
              <el-button
                v-else
                type="text"
                style="color:#67C23A;text-decoration:underline;margin-right:15px;"
                @click="editParagraph(scope.row)"
              >编辑</el-button>
              <el-button
                type="text"
                style="color:#F56C6C;text-decoration:underline;"
                @click="delParagraph(scope.row, scope.$index)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-size="pageSize"
          layout="total,prev, pager, next,sizes, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 删除 -->
    <el-dialog
      title="删除"
      :visible.sync="dialogVisibleDel"
      :modal-append-to-body="false"
      width="500px"
      top="320px"
      @close="closeDelDialog()"
    >
      <div style="display:flex;align-items:center;padding-left:50px">
        <img
          src="@/assets/<EMAIL>"
          style="width:20px;height:20px;margin-right:10px"
        >
        <div style="font-size:16px;color:#F94E4E">
          {{ delText }}
        </div>
      </div>
      <div
        slot="footer"
        class="dialog_footer"
      >
        <el-button @click="closeDelDialog()">取 消</el-button>
        <el-button
          type="primary"
          @click="handleDel()"
        >确 认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  saveDtsDevice,
  paragraphPage,
  calcParagraph,
  saveParagraph,
  delParagraph,
  clearParagraph
} from '@/api/deviceManage'
import { deepClone } from '@/utils'

export default {
  name: 'BatteryCabManage',
  components: {},
  props: {
    targetData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        baseInfo: {
          name: '',
          mac: '',
          tempResolutionRatio: '',
          refreshInterval: '',
          lineLength: '',
          fibreCoreSize: ''
        }
      },
      rules: {
        name: [
          { required: true, message: '请输入DTS主机信息', trigger: 'blur' }
        ],
        mac: [
          { required: true, message: '请输入mac地址', trigger: 'blur' }
        ],
        tempResolutionRatio: [
          { required: true, message: '请输入采样率', trigger: 'blur' }
        ],
        refreshInterval: [
          { required: true, message: '请输入刷新时间', trigger: 'blur' }
        ],
        lineLength: [
          { required: true, message: '请输入光缆长度', trigger: 'blur' }
        ],
        fibreCoreSize: [
          { required: true, message: '请输入纤芯类型', trigger: 'blur' }
        ]
      },
      startPosition: null, // 起始点
      endPosition: null, // 终点
      defaultVal: null, // 初始值
      tableData: [],
      pageNum: 1,
      sort: 0, // 排序
      pageSize: 10,
      total: 0,
      parentId: null, // 父级设备id
      editState: false, // 是否有分段数据未保存
      select: {},
      dialogVisibleDel: false,
      delText: '',
      isResolutionEdit: true // 是否能编辑采样率
    }
  },
  computed: {
    ...mapGetters(['tableHeaderStyle'])
  },
  mounted() {
    // 编辑
    if (this.targetData.id) {
      this.isResolutionEdit = false
      this.$set(this.formData.baseInfo, 'id', this.targetData.id)
      this.parentId = this.targetData.id
      this.formData.baseInfo.name = this.targetData.name
      this.formData.baseInfo.mac = this.targetData.mac
      this.formData.baseInfo.refreshInterval = this.targetData.refreshInterval
      this.formData.baseInfo.fibreCoreSize = this.targetData.fibreCoreSize
      if (this.targetData.resolutionRatio) {
        this.formData.baseInfo.tempResolutionRatio = this.targetData.resolutionRatio / 100
      }
      if (this.targetData.lineLength) {
        this.formData.baseInfo.lineLength = this.targetData.lineLength / 100
      }
      this.getList(1)
    }
  },
  methods: {
    back() {
      this.$emit('changePage', { type: 'firstPage', data: { page: '2' }})
    },
    async saveBaseInfo() {
      await this.$refs.form1.validate()
      await this.$refs.form2.validate()
      saveDtsDevice(this.formData.baseInfo).then((res) => {
        if (res.code === 200) {
          this.$message.success('成功')
          this.isResolutionEdit = false
          // 若为新增，则存取后端返回的parentId
          if (!this.parentId && this.parentId !== 0) this.parentId = res.data
        }
      })
    },
    // 新增分段
    addParagraph() {
      if (!this.startPosition && this.startPosition !== 0) {
        this.$message.warning('请输入起始点')
        return
      }
      if (!this.endPosition) {
        this.$message.warning('请输入终点')
        return
      }
      if (!this.defaultVal) {
        this.$message.warning('请输入初始值')
        return
      }
      if (!this.parentId && this.parentId !== 0) {
        this.$message.warning('请先保存基础信息')
        return
      }
      if (this.editState) {
        this.$message.warning('请先保存分段数据')
        return
      }
      // 计算点位数量
      const data = {
        dtsDeviceId: this.parentId,
        startPosition: this.startPosition,
        endPosition: this.endPosition
      }
      calcParagraph(data).then((res) => {
        const obj = {
          startPosition: this.startPosition,
          endPosition: this.endPosition,
          defaultVal: this.defaultVal,
          pointCount: res.data,
          edit: true
        }
        this.tableData.unshift(obj)
        this.editState = true
        // 清空新增数据
        this.startPosition = ''
        this.endPosition = ''
        this.defaultVal = ''
      })
    },
    // 计算点位数
    calcParagraph(row, index) {
      if ((!row.startPosition && row.startPosition !== 0) ||
      (!row.endPosition && row.endPosition !== 0)) {
        this.tableData[index].pointCount = ''
        return
      }
      const data = {
        dtsDeviceId: this.parentId,
        startPosition: row.startPosition,
        endPosition: row.endPosition
      }
      calcParagraph(data).then((res) => {
        this.tableData[index].pointCount = res.data
      }).catch(() => {
        this.tableData[index].pointCount = ''
      })
    },
    // 批量导入
    bulkImport() {

    },
    // 获取dts列表
    getList(isPage) {
      if (isPage) {
        this.pageNum = 1
      }
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        query: {
          sort: this.sort,
          dtsDeviceId: this.parentId
        }
      }
      paragraphPage(params).then((res) => {
        this.tableData = res.data.records.map((item) => {
          item.startPosition /= 100
          item.endPosition /= 100
          item.defaultVal /= 100
          return item
        })
        this.pageNum = res.data.current
        this.total = Number(res.data.total)
      })
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val
      this.editState = false
      this.getList()
    },
    handleCurrentChange(val) {
      this.pageNum = val
      this.editState = false
      this.getList()
    },
    // 编辑分段
    editParagraph(row) {
      if (this.editState) {
        this.$message.warning('请先保存分段数据')
        return
      }
      this.editState = true
      const obj = this.tableData.find((item) => item.id === row.id)
      if (obj) this.$set(obj, 'edit', true)
    },
    // 保存分段
    saveParagraph(row) {
      if (!row.startPosition && row.startPosition !== 0) {
        this.$message.warning('请输入起始点')
        return
      }
      if (!row.endPosition) {
        this.$message.warning('请输入终点')
        return
      }
      // if (Number(row.endPosition) < Number(row.startPosition)) {
      //   this.$message.warning('终点不能小于起点')
      //   return
      // }
      if (!row.defaultVal) {
        this.$message.warning('请输入初始值')
        return
      }
      const data = {
        id: row.id,
        startPosition: row.startPosition,
        endPosition: row.endPosition,
        defaultValDouble: row.defaultVal,
        pointCount: row.pointCount,
        dtsDeviceId: this.parentId
      }
      saveParagraph(data).then((res) => {
        this.$message.success('成功')
        this.editState = false
        this.getList()
      })
    },
    // 清空分段
    clearParagraph() {
      this.delText = '确认清空分段数据吗？'
      this.dialogVisibleDel = true
    },
    // 删除分段
    delParagraph(row, index) {
      if (!row.id) {
        this.tableData.splice(index, 1)
        this.editState = false
      } else {
        this.delText = '确认删除所选数据吗？'
        this.select = deepClone(row)
        this.dialogVisibleDel = true
      }
    },
    // 删除提交
    handleDel() {
      if (this.select.id) {
        const data = {
          id: this.select.id
        }
        delParagraph(data).then((res) => {
          this.$message.success('成功')
          this.editState = false
          if (this.pageNum > 1 && this.tableData.length === 1) {
            this.pageNum--
          }
          this.getList()
          this.closeDelDialog()
        })
      } else {
        clearParagraph({ deviceId: this.parentId }).then((res) => {
          this.$message.success('成功')
          this.editState = false
          this.getList(1)
          this.closeDelDialog()
        })
      }
    },
    // 删除取消
    closeDelDialog() {
      this.select = {}
      this.dialogVisibleDel = false
    }
  }
}
</script>

<style lang="scss" scoped>
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #E5E5E5;
  margin-bottom: 25px;
  .left {
    display: flex;
    align-items: center;
    font-size: 17px;
    font-weight: bold;
    font-family: PingFang SC RE;
    img {
      width: 28px;
      height: 28px;
      cursor: pointer;
      margin-right: 10px;
    }
    .pa_name {
      color: #8D95A5;
      margin-right: 5px;
    }
    .ch_name {
      color: #202225;
      margin-right: 5px;
    }
  }
}
.title {
  height: 20px;
  font-size: 17px;
  font-weight: bold;
  color: #202225;
  padding-left: 10px;
  border-left: 4px solid #1768EB;
  margin-bottom: 15px;
}
.base_info {
  font-family: PingFang SC RE;
  .content {
    border: 1px solid #E0E0E0;
    border-radius: 5px;
    padding: 20px 25px;
    padding-bottom: 0px;
    margin-bottom: 25px;
    .form_part {
      display: flex;
      .form_item {
        width: 25%;
        span {
          font-weight: bold;
          margin-left: 10px;
        }
      }
    }
  }
}
.center_btn {
  display:flex;
  justify-content:flex-end;
  padding-bottom:25px;
  border-bottom: 1px solid #E0E0E0;
  margin-bottom: 25px;
}
.table_list {
  font-family: PingFang SC RE;
  .content {
    border: 1px solid #E0E0E0;
    border-radius: 5px;
    padding: 20px;
    .list_item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .item_info {
        display: flex;
        .input_style {
          display: flex;
          align-items: center;
          margin-right: 30px;
          .text {
            font-family: PingFang SC RE;
            font-weight: bold;
            margin-left: 10px;
          }
        }
      }
      .btn_list {
        display: flex;
      }
    }
  }
}
.table_title {
  display: flex;
  align-items: center;
  justify-content: center;
  .tips {
    width:18px;
    height:18px;
    line-height: 18px;
    font-size: 13px;
    border-radius:18px;
    color:#fff;
    background:#727272;
    margin-left: 8px;
    cursor: pointer;
  }
}
</style>
