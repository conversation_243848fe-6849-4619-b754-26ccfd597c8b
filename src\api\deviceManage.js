import request from '@/utils/request'

// 驻点信息
export function detail() {
  return request({
    url: '/api/v1/dts/project/detail',
    method: 'get',
  })
}

// 电缆下拉列表
export function cableOptions(params) {
  return request({
    url: '/api/v1/cable/options',
    method: 'get',
    params,
  })
}

// 通过主键（电缆id）获取监控段的所有点位
export function monitoringPoint(id) {
  return request({
    url: `/api/v1/dts/space/segment/${id}/points`,
    method: 'get',
  })
}

// 电缆分页查询
export function cablePage(params) {
  return request({
    url: '/api/v1/cable/page',
    method: 'get',
    params,
  })
}

// 修改电缆
export function updateCable(data) {
  return request({
    url: '/api/v1/cable',
    method: 'put',
    data,
  })
}

// 煤矿列表
export function areaList() {
  return request({
    url: '/api/v1/dts/space/cable/list',
    method: 'get',
  })
}

// 添加煤矿
export function saveArea(data) {
  return request({
    url: '/api/v1/dts/space/cable',
    method: 'post',
    data,
  })
}

// 删除煤矿
export function delSpace(data) {
  return request({
    url: '/api/v1/dts/space/delSpace',
    method: 'post',
    data,
  })
}

// 删除监控段
export function delPoint(data) {
  return request({
    url: `/api/v1/dts/space/segment`,
    method: 'delete',
    data,
  })
}
// 删除全部监控段
export function delAllPoint(data) {
  return request({
    url: `/api/v1/dts/space/segment/all`,
    method: 'delete',
    params: data,
  })
}

// 工作面分页
export function cabinetPage(data) {
  return request({
    url: '/api/v1/dts/space/work-face/page',
    method: 'post',
    data,
  })
}

// 获取工作面不分页
export function workFaceList(params) {
  return request({
    url: '/api/v1/dts/space/work-face/list',
    method: 'get',
    params,
  })
}

// 添加工作面
export function saveCabinet(data) {
  return request({
    url: '/api/v1/dts/space/work-face',
    method: 'post',
    data,
  })
}

// 根据id获取工作面
export function getCabinet(id) {
  return request({
    url: `/api/v1/dts/space/work-face/${id}`,
    method: 'get',
  })
}

// 根据工作面id获取温度数据
export function getTemperatures(id) {
  return request({
    url: `/api/v1/dts/space/segment/list/temperature`,
    method: 'get',
    params: { workFaceId: id },
  })
}

// 根据工作面id获取位移数据
export function getDisplacements(id) {
  return request({
    url: '/api/v1/move-sensor/latest',
    method: 'get',
    params: { workFaceId: id },
  })
}

// 获取工作面层
export function tierAndCellList(params) {
  return request({
    url: '/api/v1/dts/space/work-face/page',
    method: 'get',
    params,
  })
}

// 添加柜层
export function addTier(data) {
  return request({
    url: '/api/v1/dts/space/addTier',
    method: 'post',
    data,
  })
}

// // 监控段位分页
// export function pointPage(data) {
//   return request({
//     url: '/api/v1/dts/space/segment/page',
//     method: 'post',
//     data,
//   })
// }
// 监控段位分页
export function pointPage(data) {
  return request({
    url: '/api/v1/dts/space/segment/page',
    method: 'get',
    params: data,
  })
}

// 监控段不分页
export function pointList(id) {
  return request({
    url: '/api/v1/dts/space/segment/list',
    method: 'get',
    params: { workFaceId: id },
  })
}

// 添加监控段位
export function addPoint(data) {
  return request({
    url: '/api/v1/dts/space/segment',
    method: 'post',
    data,
  })
}

// 修改监控段位
export function updatePoint(data) {
  return request({
    url: '/api/v1/dts/space/segment',
    method: 'put',
    data,
  })
}

// 监控段列表
// export function paragraphDropdown(params) {
//   return request({
//     url: '/api/v1/dts/space/paragraphDropdown',
//     method: 'get',
//     params,
//   })
// }
// 光纤分段列表
export function paragraphDropdown(params) {
  return request({
    url: '/api/v1/dts/paragraph/paragraphDropdown',
    method: 'get',
    params,
  })
}

// 根据监控段id查询通道
export function findChannelList(params) {
  return request({
    url: '/api/v1/dts/space/findChannelList',
    method: 'get',
    params,
  })
}

// 查询dts主机列表（DTS-光纤）
export function deviceDropdown(params) {
  return request({
    url: '/api/v1/dts/device/deviceDropdown',
    method: 'get',
    params,
  })
}

// DTS主机分页
export function devicePage(data) {
  return request({
    url: '/api/v1/dts/device/devicePage',
    method: 'post',
    data,
  })
}

// 删除dts主机
export function delDevice(data) {
  return request({
    url: '/api/v1/dts/device/delDevice',
    method: 'post',
    data,
  })
}

// 保存DTS主机信息
export function saveDtsDevice(data) {
  return request({
    url: '/api/v1/dts/device/saveDtsDevice',
    method: 'post',
    data,
  })
}

// // 分段列表
// export function paragraphPage(data) {
// /api/v1/dts/paragraph/paragraphPage
//   return request({
//     url: '/api/v1/dts/device/paragraphPage',
//     method: 'post',
//     data,
//   })
// }
// 光纤分段列表
export function paragraphPage(data) {
  return request({
    url: '/api/v1/dts/paragraph/paragraphPage',
    method: 'post',
    data,
  })
}

// 计算分段点位数量
export function calcParagraph(data) {
  return request({
    url: '/api/v1/dts/device/calcParagraph',
    method: 'post',
    data,
  })
}

// 保存分段
// export function saveParagraph(data) {
//   return request({
//     url: '/api/v1/dts/device/saveParagraph',
//     method: 'post',
//     data,
//   })
// }

// 光纤分段保存
export function saveParagraph(data) {
  return request({
    url: '/api/v1/dts/paragraph/saveParagraph',
    method: 'post',
    data,
  })
}

// 删除分段
export function delParagraph(params) {
  return request({
    url: '/api/v1/dts/paragraph/delParagraph',
    method: 'get',
    params,
  })
}

// 清空分段
export function clearParagraph(params) {
  return request({
    url: '/api/v1/dts/paragraph/clearParagraph',
    method: 'get',
    params,
  })
}

// dts导出
export function deviceExport(data) {
  return request({
    url: '/api/v1/dts/device/deviceExport',
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 监控段导出
export function pointExport(data) {
  return request({
    url: '/api/v1/dts/space/segment/export',
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 获取测温点树
export function getPointTree(params) {
  return request({
    url: '/api/v1/dts/space/work-face/tree',
    method: 'get',
    params,
  })
}
// 光纤分页
export function opticalFiberPage(data) {
  return request({
    url: '/api/v1/dts/fiber/page',
    method: 'post',
    data,
  })
}

// 光纤保存
export function opticalFiberSave(data) {
  return request({
    url: '/api/v1/dts/fiber/save',
    method: 'post',
    data,
  })
}

// 删除光纤
export function opticalFiberDel(params) {
  return request({
    url: '/api/v1/dts/fiber/del',
    method: 'get',
    params,
  })
}

// 光纤下拉框
export function opticalFiberDropdown(params) {
  return request({
    url: '/api/v1/dts/fiber/dropdown',
    method: 'get',
    params,
  })
}

// 下载线段导入模板
export function downloadParaTemplate(data) {
  return request({
    url: '/api/v1/dts/paragraph/downloadParaTemplate',
    method: 'get',
    data,
    responseType: 'blob',
  })
}

// 导入线段
export function importParagraph(data, id) {
  return request({
    url: `/api/v1/dts/paragraph/importParagraph?fiberId=${id}`,
    method: 'post',
    data,
  })
}

// 下载监控段导入模板
export function downloadPointTemplate(data) {
  return request({
    url: '/api/v1/dts/space/segment/template',
    method: 'get',
    data,
    responseType: 'blob',
  })
}

// 导入监控段
export function importPoint(data) {
  return request({
    url: `/api/v1/dts/space/segment/import`,
    method: 'post',
    data,
  })
}

// 下载工作面模板
export function downloadCabinetTemplate(data) {
  return request({
    url: '/api/v1/dts/space/work-face/template',
    method: 'get',
    data,
    responseType: 'blob',
  })
}

// 导入监控段
export function importCabinet(data) {
  return request({
    url: `/api/v1/dts/space/work-face/import`,
    method: 'post',
    data,
  })
}

// 根据工作面id获取工作面的上下级关系
export function getWorkFaceRelation(id) {
  return request({
    url: `/api/v1/dts/space/segment/relation/${id}`,
    method: 'get',
  })
}

// 点位温度历史记录
export function temperatureHistory(params) {
  return request({
    url: '/api/v1/site/position/history',
    method: 'get',
    params,
  })
}

// 导出点位温度历史记录
export function ExportTemperatureHistory(params) {
  return request({
    url: '/api/v1/dts/device/position/history/export',
    method: 'get',
    params,
    responseType: 'blob',
  })
}

// 位移点位历史记录导出
export function ExportDisplacementHistory(params) {
  return request({
    url: '/api/v1/move-sensor/history/export',
    method: 'get',
    params,
    responseType: 'blob',
  })
}

// 空间树结构
export function spaceTree() {
  return request({
    url: '/api/v1/dts/space/tree',
    method: 'get',
  })
}
