<template>
  <div style="position:relative;">
    <div
      id="lineCharts"
      style="height: calc(100vh - 9.5rem); width: 100%;"
    />
  </div>
</template>
<script>
import * as echarts from 'echarts'

export default {
  name: 'LineCharts',
  props: {
    propData: {
      require: false,
      type: Object,
      default: () => {}
    },
    page: {
      type: String,
      default: 'first'
    }
  },
  data() {
    return {
      timer: null,
      total: 10000
    }
  },
  created() {
  },
  mounted() {
    window.addEventListener('resize', this.onResize)
    this.$nextTick(() => {
      this.initcharts()
    })
  },
  destroyed() {
    window.removeEventListener('resize', this.onResize)
  },

  methods: {
    initcharts() {
      this.chart = echarts.init(document.getElementById('lineCharts'))
      this.chart.clear()
      const option = {
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        legend: {
          type: 'plain',
          top: 0,
          right: 50,
          itemGap: 50,
          itemWidth: 20,
          itemHeight: 7,
          icon: 'roundRect'
        },
        grid: {
          left: 80,
          top: 40,
          bottom: 25,
          right: 110,
          containLabel: true
        },
        dataZoom: [{
          // filterMode: 'none',
          type: 'inside'
          // start: this.zoomStartChild,
          // end: this.zoomEndChild
        },
        {
          type: 'slider', // 滑动条单独显示
          show: true, // 是否显示滑动条
          start: this.zoomStartChild,
          end: this.zoomEndChild,
          height: 8, // 滑动条组件高度
          bottom: 5, // 距离图表区域下边的距离
          borderRadius: 5,
          showDetail: true, // 拖拽时显示详情
          showDataShadow: false,
          fillerColor: 'rgba(84, 112, 198, 0.6)', // 平移条的填充颜色
          borderColor: 'transparent', // 边框颜色rgb(204, 207, 215)
          backgroundColor: '#CCCFD7', // 背景颜色
          handleStyle: {
            color: '#019e59', // 手柄颜色
          },
          textStyle: {
            fontSize: 12,
          },
        }],
        xAxis: {
          name: '时间',
          type: 'category',
          data: this.propData.xAxisData,
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14
          },
          axisLine: {
            lineStyle: {
              color: '#8F98A0'
            }
          },
          axisTick: { length: 0 },
          axisLabel: {
            textStyle: {
              color: '#909090'
            },
            fontSize: 14
            // formatter: (value) => `${value.split(' ')[1]}`
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        yAxis: {
          name: '温度：℃',
          type: 'value',
          axisLine: { show: false },
          axisLabel: {
            color: '#9FA3AB'
          },
          nameTextStyle: {
            color: '#9FA3AB',
            fontSize: 14,
            align: 'center',
            padding: [0, 0, 5, 0]
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          splitNumber: 10
        },
        tooltip: {
          trigger: 'axis',
          enterable: true,
          padding: [12, 15, 20, 20],
          textStyle: { color: '#424242' },
          renderMode: 'html',
          formatter(params) {
            let html = ``
            params.forEach((v) => {
              html += `<div>
                  <div style="display:flex;align-items:center">
                    <div
                      style="
                        margin-right:10px;
                        border-radius:6px;
                        width:23px;height:7px;
                        background-color:${v.color};
                      "
                    ></div>
                    <div style="margin-right:15px;">
                      ${`${v.name}`}
                    </div>
                    <div>
                      ${`${(v.data.value !== null && v.data.value !== undefined) ? v.data.value : '--'}℃`}
                    </div>
                  </div>
                </div>`
            })

            return html
          },
          className: 'tooltip'
        },
        series: this.propData.seriesData
      }
      this.chart.clear()
      this.chart.setOption(option)
    },
    onResize() {
      this.chart.resize()
    }

  }
}
</script>
<style lang="scss" scoped>
.remark {
  position: absolute;
  top: 0;
  right: 20px;
  display: flex;
  justify-content: space-between;
  width: 180px;
  height: 20px;
  // line-height: 50px;
  .remark_item {
    display: flex;
    align-items: center;
    .dot {
      // width: 21px;
      // height: 6px;
      // margin-right: 5px;
      // border-radius: 2px;
      width: 10px;
      height: 10px;
      margin-right: 5px;
      border-radius: 10px;
    }
    .dot1 {
      background-color: #00E667;
    }
    .dot2 {
      background-color: #F95F5F;
    }
    .text {
      color: black;
      font-family: PingFang SC RE;
      // font-weight: bold;
      font-size: 14px;
    }
  }
}
</style>
