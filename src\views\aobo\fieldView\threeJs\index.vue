<template>
  <div class="threeOut">
    <div ref="container" class="threeJs_container" />

    <transition appear name="fade">
      <div v-if="!threeJsLoaded" class="loading-text">{{ Math.floor(threeJsProgress) }}%</div>
    </transition>

    <!-- 实时曲线图表数据 -->
    <el-dialog v-if="dialogVisible" title="详情" :visible.sync="dialogVisible" width="60vw" append-to-body destroy-on-close top="90px">
      <MarkerDetail :temp-level-list="tempLevelList" :data="formData" />
    </el-dialog>
  </div>
</template>

<script>
import { debounce } from '@/utils'
import { getAlarmLevelList } from '@/api/alarmTactics'
import { camera, Core, css2dRenderer, environment, orbitControls, postProcess, renderer, scene } from '@/views/aobo/fieldView/threeJs/service/core'
import { RayCasterController } from '@/views/aobo/fieldView/threeJs/service/rayCasterController'
import { mapGetters } from 'vuex'
import Stats from 'three/examples/jsm/libs/stats.module'
import * as THREE from 'three'
import gsap from 'gsap'
import Vue from 'vue'
import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer'
import MarkerMarkerDetail from '@/views/aobo/fieldView/threeJs/components/MarkerDetail.vue'
import SegmentPopup from '../components/SegmentPopup.vue'
import TubePopup from '../components/TubePopup.vue'
import AlarmMarkerPopup from '../components/AlarmMarkerPopup.vue'

const alarmColorMap = {
  0: '#00A1FF',
  1: '#FF7F00',
  2: '#FFFF00',
  3: '#FF0000',
}

// 每隔多少米一个点
const segmentLength = 1
let count = 0

export default {
  name: 'LineMap',
  components: { MarkerDetail: MarkerMarkerDetail },
  props: {
    pipelineList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      core: new Core(),
      rayCasterController: null,
      debouncedResizeHandler: null,

      // 所有分段管道
      tubes: [],
      // 存储动画ID，用于清理
      segmentAnimationIds: [],
      alarmAnimationId: null,

      // 选中告警点位的扩散效果
      sphereExpansion: null,

      // InstancedMesh 相关
      alarmInstancedMesh: null,
      alarmInstanceData: [], // 存储每个实例的数据
      alarmGeometry: null,
      alarmMaterial: null,
      maxAlarmCount: 10000, // 最大告警点位数量

      // 创建变换矩阵
      dummy: new THREE.Object3D(),

      offSegmentClick: () => {},
      offTubeClick: () => {},
      offAlarmClick: () => {},

      dialogVisible: false,
      dialogVisibleMarker: false,
      formData: null,
      loading: false,

      currentPopup: null, // 当前显示的弹窗
      popupType: null,
      popupData: null,

      alarmLevelList: [],
      tempLevelList: [],
    }
  },
  computed: {
    ...mapGetters(['threeJsLoaded', 'threeJsProgress']),
  },

  watch: {
    pipelineList() {
      if (this.threeJsLoaded) {
        this.drawSegments()
      }
    },
    threeJsLoaded(newVal) {
      if (!newVal) return
      this.offTubeClick = this.rayCasterController.bindClickRayCastObj(environment.tubeList, this.onTubeClick)
      if (this.pipelineList?.length) {
        this.drawSegments()
      }
    },
  },
  created() {
    this.getAlarmLevelList()
    postProcess.init()
  },
  mounted() {
    const containerRef = this.$refs.container
    containerRef.appendChild(renderer.domElement)
    containerRef.appendChild(css2dRenderer.domElement)
    this.onResize()
    this.rayCasterController = new RayCasterController(camera, this.$refs.container)

    // const stats = new Stats()
    // const statsDom = stats.domElement
    // statsDom.classList.add('stats')
    // document.body.appendChild(statsDom)
    //
    // const updateState = () => {
    //   stats.update()
    //
    //   requestAnimationFrame(() => {
    //     updateState()
    //   })
    // }
    //
    // requestAnimationFrame(updateState)
  },
  beforeDestroy() {
    this.closeCurrentPopup()
    this.clearSegments()
    this.offTubeClick()
    this.core.dispose()
    window.removeEventListener('resize', this.debouncedResizeHandler)
  },

  methods: {
    // 窗口大小变化后重新设置threeJs画布尺寸
    onResize() {
      const containerRef = this.$refs.container
      const resizeHandler = () => {
        const { width, height } = containerRef.getBoundingClientRect()
        this.core.changeSize(width, height)
      }
      resizeHandler()
      this.debouncedResizeHandler = debounce(resizeHandler, 100, false)
      window.addEventListener('resize', this.debouncedResizeHandler)
    },

    /**
     * 获取等级列表
     * */
    getAlarmLevelList() {
      getAlarmLevelList().then((res) => {
        this.alarmLevelList = res.data
        // 根据value排序，大的在前
        this.tempLevelList = res.data[0].levels.sort((a, b) => b.value - a.value)
      })
    },

    /**
     * 初始化告警点位的 InstancedMesh
     */
    initAlarmInstancedMesh() {
      // 创建共享几何体
      this.alarmGeometry = new THREE.SphereGeometry(0.15, 32, 16)
      this.alarmGeometry.computeBoundsTree()

      // 创建实心球体材质
      this.alarmMaterial = new THREE.MeshPhongMaterial({
        transparent: false,
        side: THREE.DoubleSide,
      })

      // 创建 InstancedMesh
      this.alarmInstancedMesh = new THREE.InstancedMesh(this.alarmGeometry, this.alarmMaterial, this.maxAlarmCount)
      this.alarmInstancedMesh.count = 0

      // 添加到场景
      scene.add(this.alarmInstancedMesh)
    },

    /**
     * 清理管道、材质和动画
     */
    clearSegments() {
      // 清理事件监听
      this.offSegmentClick()
      this.offAlarmClick()
      // 清理动画
      this.segmentAnimationIds.forEach((id) => {
        if (id) {
          cancelAnimationFrame(id)
        }
      })
      this.segmentAnimationIds = []

      // 清理管道
      this.tubes.forEach((tube) => {
        // 从场景中移除
        scene.remove(tube)

        // 清理几何体
        if (tube.geometry) {
          tube.geometry.dispose()
        }

        // 清理材质
        if (tube.material) {
          if (tube.material.map) {
            tube.material.map.dispose()
          }
          tube.material.dispose()
        }

        // 清理用户数据
        tube.userData = null
      })
      this.tubes = []

      // 清理 InstancedMesh
      if (this.alarmInstancedMesh) {
        this.alarmInstancedMesh.dispose()
        scene.remove(this.alarmInstancedMesh)
        this.alarmInstancedMesh = null
      }
      if (this.alarmGeometry) {
        this.alarmGeometry.dispose()
        this.alarmGeometry = null
      }
      if (this.alarmMaterial) {
        this.alarmMaterial.dispose()
        this.alarmMaterial = null
      }
      this.alarmInstanceData = []

      // 清理后处理选择对象
      if (postProcess.bloomPass?.selection) {
        postProcess.bloomPass.selection.clear()
        postProcess.outlinePass.selection.clear()
      }
    },

    /**
     * 绘制电缆分段
     * */
    drawSegments() {
      // 先清理之前的管道
      this.clearSegments()
      count = 0

      // 初始化告警点位的 InstancedMesh
      this.initAlarmInstancedMesh()

      let needUpdatePopupData = false
      for (const item of this.pipelineList) {
        if (this.popupType === 'tube' && (this.popupData?.code === item.code)) {
          needUpdatePopupData = true
          this.popupData = { ...this.popupData, ...item }
          this.currentPopup.vueInstance.data = this.popupData
        }
        if (!item.segments?.length) continue
        const curve = environment.curveMap[item.code]
        if (!curve) {
          this.$message.warning(`未找到对应的曲线: ${item.code}`)
          continue
        }

        const totalLength = curve.getLength()
        for (const segment of item.segments) {
          if (segment.startPosition / 100 >= totalLength) continue
          if (this.popupType === 'segment' && (this.popupData?.id === segment.id)) {
            needUpdatePopupData = true
            this.popupData = { ...this.popupData, ...segment }
            this.currentPopup.vueInstance.data = this.popupData
          }

          const startDistance = segment.startPosition / 100
          const endDistance = Math.min(segment.endPosition / 100, totalLength)
          const segmentT = segmentLength / totalLength
          const pointCount = Math.max(Math.floor((endDistance - startDistance) / segmentLength) - 1, 0)
          const startT = startDistance / totalLength
          const segmentPoints = [curve.getPoint(startT)]
          for (let i = 1; i <= pointCount; i++) {
            const t = startT + i * segmentT
            segmentPoints.push(curve.getPoint(t))
          }

          segmentPoints.push(curve.getPoint(endDistance / totalLength))
          const segmentCurve = new THREE.CatmullRomCurve3(segmentPoints)
          const segmentTubeGeometry = new THREE.TubeGeometry(segmentCurve, pointCount + 1, 0.12, 12, false)
          segmentTubeGeometry.computeBoundsTree()

          // 创建管道材质，使用加载的纹理
          const segmentTubeMaterial = new THREE.MeshLambertMaterial({
            map: segment.alarmLevel === null ? environment.textureObj.status[0] : environment.textureObj.status[segment.alarmLevel],
            side: THREE.DoubleSide,
            opacity: 0.8, // 设置透明度，如果需要半透明效果
            transparent: true, // 启用透明度

            // depthWrite: false, // 禁用深度写入以实现透明效果
            // depthTest: false, // 启用深度测试以确保正确渲染
          })

          // const segmentTubeMaterial = new THREE.MeshPhysicalMaterial({
          //   map: segment.alarmLevel === null ? environment.textureObj.status[0] : environment.textureObj.status[segment.alarmLevel],
          //   transmission: 0.5, // 设置透射度，1.0表示完全透明
          //   ior: 1.5, // 设置折射率，玻璃的典型值
          //   roughness: 0.8, // 设置粗糙度，0.0表示完全光滑
          //   metalness: 0.5, // 设置金属度，0.0表示非金属
          //   color: 0xffffff, // 设置颜色，可以根据需要调整
          //   side: THREE.DoubleSide,
          //   // opacity: 0.5, // 设置透明度，如果需要半透明效果
          //   transparent: true // 启用透明度
          // })

          const segmentTube = new THREE.Mesh(segmentTubeGeometry, segmentTubeMaterial)
          segmentTube.name = item.name
          segment.parentCube = item.name
          segment.parentData = item
          segmentTube.userData.data = segment
          segmentTube.userData.code = item.code
          // tube.visible = false
          scene.add(segmentTube)
          this.tubes.push(segmentTube)

          // 添加告警点位
          if (segment.alarms?.length) {
            for (const alarm of segment.alarms) {
              if (this.popupType === 'alarm' && (this.popupData?.cablePosition === alarm.cablePosition)) {
                needUpdatePopupData = true
                this.popupData = { ...this.popupData, ...alarm }
                this.currentPopup.vueInstance.data = this.popupData
                this.sphereExpansion.material.color = new THREE.Color(alarmColorMap[alarm.level])
              }
              alarm.parentData = segment
              this.drawAlarmMarker(curve, alarm)
            }
          }

          // 如果告警添加闪烁动画
          if (segment.alarmLevel !== null) {
            postProcess.outlinePass.selection.add(segmentTube)
            const animationIndex = this.segmentAnimationIds.length
            this.segmentAnimationIds.push(null) // 先占位

            const animate = () => {
              const intensity = (Math.sin(Date.now() * 0.01) + 1) / 2
              segmentTubeMaterial.emissive.setRGB(0.5 * intensity, 0, 0)
              segmentTubeMaterial.opacity = 0.6 + 0.4 * intensity // 透明度从0.6到1.0之间变化
              this.segmentAnimationIds[animationIndex] = requestAnimationFrame(animate)
            }

            animate()
          }
        }
      }

      if (!needUpdatePopupData) {
        this.closeCurrentPopup()
      }

      this.offSegmentClick = this.rayCasterController.bindClickRayCastObj(this.tubes, this.onSegmentClick)
      this.offAlarmClick = this.rayCasterController.bindClickRayCastObj([this.alarmInstancedMesh], this.onAlarmClick)
      console.log(count, '告警点总数')
    },

    /**
     * 绘制告警点位
     * */
    drawAlarmMarker(curve, data) {
      count++

      const t = Math.min(data.cablePosition / (curve.getLength() * 100), 1)
      const alarmPoint = curve.getPoint(t)

      // 添加到实例数据数组
      const instanceIndex = this.alarmInstanceData.length
      this.alarmInstanceData.push({
        position: alarmPoint,
        data,
        instanceIndex,
      })

      this.dummy.scale.set(1, 1, 1)
      this.dummy.position.copy(alarmPoint)
      this.dummy.updateMatrix()

      // 更新实心球体实例
      this.alarmInstancedMesh.setMatrixAt(instanceIndex, this.dummy.matrix)
      this.alarmInstancedMesh.setColorAt(instanceIndex, new THREE.Color(alarmColorMap[data.level]))
      this.alarmInstancedMesh.count = this.alarmInstanceData.length

      // 标记需要更新
      this.alarmInstancedMesh.instanceMatrix.needsUpdate = true
      this.alarmInstancedMesh.instanceColor.needsUpdate = true

      // 将数据存储到 userData 中以供点击检测使用
      if (!this.alarmInstancedMesh.userData.instanceData) {
        this.alarmInstancedMesh.userData.instanceData = []
      }
      this.alarmInstancedMesh.userData.instanceData[instanceIndex] = data
    },

    /**
     * 点击告警点位
     * */
    onAlarmClick(obj, _, instanceId) {
      // 先关闭上一个弹窗
      this.closeCurrentPopup()

      const data = obj.userData.instanceData[instanceId]

      // 告警点中心点
      const position = this.alarmInstanceData[instanceId].position.clone()

      const popup = this.addPopup(AlarmMarkerPopup, position, data)
      this.popupType = 'alarm'
      popup.vueInstance.$on('detail', (row) => {
        this.openDetail(row)
      })

      const { t } = this.getClosestPointOnCurve(environment.centerCurve, position)
      const positive = t >= 0.8 ? -1 : 1
      const cameraPosition = environment.centerCurve.getPoint(t + 0.002 * positive)
      gsap.to(orbitControls.target, {
        x: position.x,
        y: position.y,
        z: position.z,
        duration: 0.5,
        ease: 'none',
        onUpdate: () => {
          // orbitControls.update()
        },
      })
      gsap.to(camera.position, {
        x: cameraPosition.x,
        y: cameraPosition.y,
        z: cameraPosition.z,
        duration: 0.5,
        ease: 'none',
      })

      /**
       *  在告警中心点添加一个扩散效果
       * */
      const sphereGeometry = new THREE.SphereGeometry(0.15, 32, 16)
      sphereGeometry.computeBoundsTree()

      // 创建动画球体材质
      const sphereMaterial = new THREE.MeshPhongMaterial({
        transparent: true,
        color: new THREE.Color(alarmColorMap[data.level]),
        opacity: 0.8,
        depthWrite: false,
        depthTest: false,
      })
      const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial)
      this.sphereExpansion = sphere
      sphere.position.copy(position)
      scene.add(sphere)
      const animationStartTime = Date.now()
      const animationDuration = 1000 // 1秒一个周期
      const maxScale = 3 // 最大缩放倍数
      const maxOpacity = 0.8 // 最大透明度（开始时）
      const minOpacity = 0 // 最小透明度（结束时）

      const animateExpansion = () => {
        cancelAnimationFrame(this.alarmAnimationId)
        const elapsed = Date.now() - animationStartTime
        const progress = (elapsed % animationDuration) / animationDuration

        // 使用缓动函数让动画更自然
        const easeProgress = progress // 线性进度，从0到1

        // 计算当前缩放比例（从1到maxScale）
        const currentScale = 1 + (maxScale - 1) * easeProgress
        sphere.scale.set(currentScale, currentScale, currentScale)

        // 计算当前透明度（从maxOpacity到minOpacity）
        const currentOpacity = maxOpacity - (maxOpacity - minOpacity) * easeProgress
        sphereMaterial.opacity = currentOpacity

        this.alarmAnimationId = requestAnimationFrame(animateExpansion)
      }

      animateExpansion()
    },

    /**
     * 关闭当前弹窗
     * */
    closeCurrentPopup() {
      if (this.currentPopup) {
        this.popupType = null
        scene.remove(this.currentPopup)
        this.currentPopup.vueInstance.$destroy()
        this.currentPopup.element?.remove()
        this.currentPopup = null
        if (this.sphereExpansion) {
          cancelAnimationFrame(this.alarmAnimationId)
          this.alarmAnimationId = null

          scene.remove(this.sphereExpansion)
          this.sphereExpansion.geometry.dispose()
          this.sphereExpansion.material.dispose()
          this.sphereExpansion = null
        }
      }
    },

    /**
     * 添加弹窗
     * */
    addPopup(component, position, data) {
      this.popupData = data

      const CompConstructor = Vue.extend(component)
      const div = document.createElement('div')
      const instance = new CompConstructor({
        el: div,
        propsData: { data },
      })

      // 监听组件内部emit的close事件
      instance.$on('close', () => {
        this.closeCurrentPopup()
      })

      const popupEl = instance.$el
      const popup = new CSS2DObject(popupEl)
      const popupPosition = position.clone()
      popup.position.copy(popupPosition)

      // 给弹窗注册点击事件
      popupEl.addEventListener('mousedown', (e) => {
        // 仅针对鼠标左键点击
        if (e.button !== 0) return
        e.stopPropagation()
      })

      scene.add(popup)

      // 保存 Vue 组件实例引用，用于正确销毁
      popup.vueInstance = instance

      // 设置为当前弹窗
      this.currentPopup = popup

      return popup
    },

    /**
     * 点击管道显示管道详情
     * */
    onTubeClick(obj, point) {
      const data = this.pipelineList.find((item) => item.code === obj.name)
      if (!data) {
        this.$message.warning('未找到对应的管道数据')
        return
      }

      // postProcess.bloomPass.selection.set([obj])

      // 聚焦到管道上
      const position = point.clone()
      // 先关闭上一个弹窗
      this.closeCurrentPopup()

      const { t } = this.getClosestPointOnCurve(environment.centerCurve, point)

      const segmentCurve = environment.curveMap[data.code]
      const pointInfo = this.getClosestPointOnCurve(segmentCurve, point)
      const length = segmentCurve.getLength() * pointInfo.t
      data.pointPosition = length
      this.addPopup(TubePopup, point, data)
      this.popupType = 'tube'

      const positive = t >= 0.8 ? -1 : 1
      const cameraPosition = environment.centerCurve.getPoint(t + 0.002 * positive)
      gsap.to(orbitControls.target, {
        x: position.x,
        y: position.y,
        z: position.z,
        duration: 0.5,
        ease: 'none',
        onUpdate: () => {
          // orbitControls.update()
        },
      })
      gsap.to(camera.position, {
        x: cameraPosition.x,
        y: cameraPosition.y,
        z: cameraPosition.z,
        duration: 0.5,
        ease: 'none',
      })
    },

    /**
     * 点击分段事件
     * */
    onSegmentClick(obj, point) {
      // postProcess.bloomPass.selection.set([obj])
      // 先关闭上一个弹窗
      this.closeCurrentPopup()

      const { data, code } = obj.userData
      // 聚焦到管道上
      const position = point.clone()

      const { t } = this.getClosestPointOnCurve(environment.centerCurve, point)
      const segmentCurve = environment.curveMap[code]
      const pointInfo = this.getClosestPointOnCurve(segmentCurve, point)
      const length = segmentCurve.getLength() * pointInfo.t
      data.pointPosition = length
      const popup = this.addPopup(SegmentPopup, point, data)
      this.popupType = 'segment'
      popup.vueInstance.$on('detail', (row) => {
        this.openDetail({ parentData: row, temperature: row.pointTemperature })
      })

      const positive = t >= 0.8 ? -1 : 1
      const cameraPosition = environment.centerCurve.getPoint(t + 0.002 * positive)
      gsap.to(orbitControls.target, {
        x: position.x,
        y: position.y,
        z: position.z,
        duration: 0.5,
        ease: 'none',
        onUpdate: () => {
          // orbitControls.update()
        },
      })
      gsap.to(camera.position, {
        x: cameraPosition.x,
        y: cameraPosition.y,
        z: cameraPosition.z,
        duration: 0.5,
        ease: 'none',
      })
    },

    /**
     * 获取曲线上距离指定点最近的点
     * @param {THREE.Curve} curve - Three.js曲线对象
     * @param {THREE.Vector3} targetPoint - 目标点
     */
    getClosestPointOnCurve(curve, targetPoint) {
      // 使用二分法搜索最近点
      let left = 0
      let right = 1
      let bestT = 0.5
      let bestDistance = Infinity

      // 三分搜索法
      for (let iteration = 0; iteration < 29; iteration++) {
        const t1 = left + (right - left) / 3
        const t2 = right - (right - left) / 3

        const point1 = curve.getPoint(t1)
        const point2 = curve.getPoint(t2)

        const dist1 = point1.distanceTo(targetPoint)
        const dist2 = point2.distanceTo(targetPoint)

        if (dist1 < dist2) {
          right = t2
          if (dist1 < bestDistance) {
            bestDistance = dist1
            bestT = t1
          }
        } else {
          left = t1
          if (dist2 < bestDistance) {
            bestDistance = dist2
            bestT = t2
          }
        }
      }

      return {
        point: curve.getPoint(bestT),
        t: bestT,
      }
    },

    /**
     * 点击线打开详情
     * */
    openDetail(row) {
      this.formData = { ...row }
      this.dialogVisible = true
    },

    /**
     * 供外部调用，定位到具体的告警点
     * */
    goToAlarmMarker(id) {
      for (const item of this.alarmInstanceData) {
        if (item.data.id === id) {
          this.onAlarmClick(this.alarmInstancedMesh, item.position, item.instanceIndex)
          return
        }
      }

      this.$message.warning('未找到对应的告警点')
    },

    /**
     * 供外部调用，传入t值定位到具体的位置
     * */
    toPosition(t) {
      const position = environment.centerCurve.getPoint(Math.min(t, 1))
      const positive = t >= 0.8 ? -1 : 1
      const cameraPosition = environment.centerCurve.getPoint(t + 0.008 * positive)

      gsap.to(orbitControls.target, {
        x: position.x,
        y: position.y,
        z: position.z,
        duration: 0.5,
        ease: 'none',
        onUpdate: () => {
          // orbitControls.update()
        },
      })
      gsap.to(camera.position, {
        x: cameraPosition.x,
        y: cameraPosition.y,
        z: cameraPosition.z,
        duration: 0.5,
        ease: 'none',
      })
    }

  },
}
</script>

<style scoped lang="scss">
.threeOut {
  position: relative;
  height: 100%;
}
.threeJs_container {
  height: 100%;
  position: absolute;
  inset: 0;
  z-index: 9;
}

.loading-text {
  position: absolute;
  inset: 0;
  display: grid;
  place-items: center;
  font-size: 80px;
  z-index: 999;
  user-select: none;
  color: #bdbcbc;
  text-shadow: 0 1px 0 hsl(174, 5%, 80%), 0 2px 0 hsl(174, 5%, 75%), 0 3px 0 hsl(174, 5%, 70%), 0 4px 0 hsl(174, 5%, 66%), 0 5px 0 hsl(174, 5%, 64%),
    0 6px 0 hsl(174, 5%, 62%), 0 7px 0 hsl(174, 5%, 61%), 0 8px 0 hsl(174, 5%, 60%), 0 0 5px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.2),
    0 3px 5px rgba(0, 0, 0, 0.2), 0 5px 10px rgba(0, 0, 0, 0.2), 0 10px 10px rgba(0, 0, 0, 0.2), 0 20px 20px rgba(0, 0, 0, 0.3);
}
</style>
