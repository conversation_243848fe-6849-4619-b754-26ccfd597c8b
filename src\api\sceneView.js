import request from '@/utils/request'

// 区域下拉框
export function areaDropdown() {
  return request({
    url: '/api/v1/dts/device/areaDropdown',
    method: 'get'
  })
}

// 根据空间id获取检查点列表
export function checkPointList(params) {
  return request({
    url: '/api/v1/dts/device/checkPointList',
    method: 'get',
    params
  })
}

// 查询报警分页
export function doPage(data) {
  return request({
    url: '/api/v1/dts/alarm/doPage',
    method: 'get',
    params: data
  })
}
// 监控段报警分页
export function segmentAlarmPage(data) {
  return request({
    url: '/api/v1/dts/alarm/segment/page',
    method: 'post',
    data
  })
}

// 报警工作面分页
export function batteryCabinetPage(data) {
  return request({
    url: '/api/v1/dts/alarm/batteryCabinetPage',
    method: 'post',
    data
  })
}

// 批量处理告警
export function disposeAlarm(data) {
  return request({
    url: '/api/v1/dts/alarm/disposeAlarm',
    method: 'post',
    data
  })
}

// 一键处置所有报警
export function disposeAllAlarm(data) {
  return request({
    url: '/api/v1/dts/alarm/disposeAllAlarm',
    method: 'post',
    data
  })
}

// 批量导出
export function batchExport(data) {
  return request({
    url: '/api/v1/dts/alarm/export',
    method: 'post',
    timeout: 60 * 60 * 1000,
    data,
    responseType: 'blob'
  })
}

// 获取报警树(至多到工作面)
export function getAlarmTree() {
  return request({
    url: '/api/v1/dts/alarm/alarmTree',
    method: 'get'
  })
}

// 批量删除
export function del(data) {
  return request({
    url: '/api/v1/dts/alarm',
    method: 'delete',
    data
  })
}
