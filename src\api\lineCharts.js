import request from '@/utils/request'

// 设备下拉框（）
export function deviceDropdown() {
  return request({
    url: '/api/v1/dts/device/deviceDropdown',
    method: 'get',
  })
}

// 空间级联下拉框
export function spaceCascadeDropdown() {
  return request({
    url: '/api/v1/dts/device/spaceCascadeDropdown',
    method: 'get',
  })
}

// 煤矿分段级联查询
export function spaceParagraphDropdown() {
  return request({
    url: '/api/v1/dts/device/spaceParagraphDropdown',
    method: 'get',
  })
}

// 获取报警阈值
export function dtsAlarmThreshold() {
  return request({
    url: '/api/v1/alarm-strategy/audio',
    method: 'get',
  })
}

// 实时趋势
export function liveTrend(data) {
  return request({
    // url: '/api/v1/dts/device/liveTrend',
    url: '/api/v1/chart/liveTrend',
    method: 'post',
    data,
  })
}

// 单点折线
export function singleLine(data) {
  return request({
    url: '/api/v1/chart/singleLine',
    method: 'post',
    data,
  })
}

// 点位历史温度
export function pointHistory(params) {
  return request({
    url: '/api/v1/move-sensor/history',
    method: 'get',
    params,
  })
}

// 传感器点位树级
export function sensorTree() {
  return request({
    url: '/api/v1/move-sensor/point/tree',
    method: 'get',
  })
}
