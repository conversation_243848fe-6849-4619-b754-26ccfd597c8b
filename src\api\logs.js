import request from '@/utils/request'

// 登录日志
export function page(data) {
  return request({
    url: '/api/v1/sys/loginLog/page',
    method: 'post',
    data,
  })
}

// 操作日志
export function portDropDownBox(data) {
  return request({
    url: '/api/v1/sys/loginLog/portDropDownBox',
    method: 'get',
    params: data,
  })
}

// 操作日志（新）
export function getList(data) {
  return request({
    url: '/api/v1/sys/log/page',
    method: 'post',
    data,
  })
}
