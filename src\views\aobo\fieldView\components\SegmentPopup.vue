<template>
  <div>
    <div class="segment-popup" style="transform: translateY(-50%)">
      <header class="title" style="display: flex; justify-content: space-between; align-items: center; pointer-events: auto;gap: 10px">
        <el-tooltip :content="data.name">
          <span class="truncate">{{ data.name }}</span>
        </el-tooltip>
        <i class="el-icon-circle-close" style="cursor: pointer" @click="close" />
      </header>
      <section class="content" style="pointer-events: auto">
        <div>
          <div>
            <span>位置：</span>
            <span>{{ data.parentCube }}-{{ Math.round(data.pointPosition * 100) / 100 }}m处</span>
          </div>

          <div style="margin-top: 12px;">
            <span>测温段范围：</span>
            <span>{{ data.startPosition / 100 }}m~{{ data.endPosition / 100 }}m</span>
          </div>

          <div style="display: flex; align-items: center; gap: 60px; margin-top: 12px">
            <div>
              <span>测温段温度：</span>
              <span v-if="data.maxTemperature !== null">{{ data.maxTemperature / 100 }}℃</span>
              <span v-else>--</span>
            </div>

            <div>
              <span>电流：</span>
              <span v-if="data.current !== null">{{ data.current }}A</span>
              <span v-else>--</span>
            </div>
          </div>

          <div style="margin-top: 12px">
            <span>点位温度：</span>
            <span v-if="pointTemperature !== null"> {{ pointTemperature }}℃</span>
            <span v-else>--</span>
            <i v-if="loading" class="el-icon-loading" style="margin-left: 5px;"></i>
          </div>

          <div style="margin-top: 12px;">
            <span>告警等级：</span>
            <span v-if="data.alarmLevel !== null" :style="{color: alarmColorMap[data.alarmLevel]}">{{ alarmLevelMap[data.alarmLevel] }}</span>
            <span v-else style="color: greenyellow">未告警</span>
          </div>

          <div style="margin-top: 12px">
            <span>时间：</span>
            <span v-if="data.time !== null">{{ data.time }}</span>
            <span v-else>--</span>
          </div>

          <div style="margin-top: 12px; text-align: center">
            <el-button type="primary" @click="detail">详情</el-button>
          </div>

        </div>
      </section>
      <footer style="display: flex; justify-content: center; margin-top: 8px">
        <img src="@/assets/onSiteView/marker.webp" style="width: 38px; height: 42px" class="marker" />
      </footer>
    </div>
  </div>
</template>

<script>
import { alarmColorMap, alarmLevelMap } from '@/constants'
import { getPointTemp } from '@/api/onSiteView'

export default {
  name: 'SegmentPopup',
  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      pointTemperature: null,
      loading: false,

    }
  },
  computed: {
    alarmColorMap() {
      return alarmColorMap
    },
    alarmLevelMap() {
      return alarmLevelMap
    },
    temperature() {
      return this.data.temperature
    },
  },
  created() {
    this.getPointTemp()
  },
  methods: {
    getPointTemp() {
      const { data } = this
      this.loading = true
      getPointTemp(data.id, Math.round(data.pointPosition * 100 - data.startPosition)).then((res) => {
        this.data.pointTemperature = res.data
        this.pointTemperature = res.data / 100
      }).finally(() => {
        this.loading = false
      })
    },

    close() {
      this.$emit('close')
    },

    detail() {
      this.$emit('detail', this.data)
    },

  },
}
</script>
<style scoped lang="scss">
.segment-popup {
  .title {
    width: 327px;
    height: 44px;
    line-height: 44px;
    background: url(~@/assets/onSiteView/title-bg.webp) center / 100% 100% no-repeat;
    color: #2187ff;
    font-size: 20px;
    font-weight: bold;
    padding-left: 76px;
    padding-right: 10px;
  }

  .content {
    padding: 12px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, #000 100%);
    color: white;
    font-size: 14px;
  }
}

</style>
