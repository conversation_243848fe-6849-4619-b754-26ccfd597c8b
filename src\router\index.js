import Vue from 'vue'
import Router from 'vue-router'

/* Layout */
import Layout from '@/layout'

Vue.use(Router)

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
 roles: ['admin','editor']    control the page roles (you can set multiple roles)
 title: 'title'               the name show in sidebar and breadcrumb (recommend set)
 icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
 breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
 activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
 }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true,
  },
  {
    path: '/forgotPassword',
    component: () => import('@/views/aobo/forgotPassword/index'),
    hidden: true,
  },
]

export const asyncRoutes = [
  {
    path: '/fieldView',
    component: Layout,
    redirect: '/aobo/fieldView',
    meta: {
      iconDefault: require('@/assets/menu_icon/<EMAIL>'),
      iconActive: require('@/assets/menu_icon/<EMAIL>'),
      iconImage: require('@/assets/menu_icon/<EMAIL>'),
      code: 'XCST',
    },
    children: [
      {
        path: 'fieldView',
        name: 'fieldView',
        component: () => import('@/views/aobo/fieldView/index'),
        meta: {
          title: '现场视图',
          iconDefault: require('@/assets/menu_icon/<EMAIL>'),
          iconActive: require('@/assets/menu_icon/<EMAIL>'),
          iconImage: require('@/assets/menu_icon/<EMAIL>'),
          code: 'XCST',
        },
      },
    ],
  },
  {
    path: '/curveView',
    component: Layout,
    redirect: '/aobo/curveView',
    meta: {
      iconDefault: require('@/assets/menu_icon/<EMAIL>'),
      iconActive: require('@/assets/menu_icon/<EMAIL>'),
      iconImage: require('@/assets/menu_icon/<EMAIL>'),
      code: 'QXSTFX',
    },
    children: [
      {
        path: 'curveView',
        name: 'curveView',
        component: () => import('@/views/aobo/curveView/index'),
        meta: {
          title: '曲线视图分析',
          iconDefault: require('@/assets/menu_icon/<EMAIL>'),
          iconActive: require('@/assets/menu_icon/<EMAIL>'),
          iconImage: require('@/assets/menu_icon/<EMAIL>'),
          code: 'QXSTFX',
        },
      },
    ],
  },
  {
    path: '/alarmInfo',
    component: Layout,
    redirect: '/aobo/alarmInfo',
    meta: {
      iconDefault: require('@/assets/menu_icon/<EMAIL>'),
      iconActive: require('@/assets/menu_icon/<EMAIL>'),
      iconImage: require('@/assets/menu_icon/<EMAIL>'),
      code: 'BJXX',
    },
    children: [
      {
        path: 'alarmInfo',
        name: 'alarmInfo',
        component: () => import('@/views/aobo/alarmInfo/index'),
        meta: {
          title: '报警信息',
          iconDefault: require('@/assets/menu_icon/<EMAIL>'),
          iconActive: require('@/assets/menu_icon/<EMAIL>'),
          iconImage: require('@/assets/menu_icon/<EMAIL>'),
          code: 'BJXX',
        },
      },
    ],
  },
  {
    path: '/deviceManage',
    component: Layout,
    redirect: '/aobo/deviceManage',
    meta: {
      iconDefault: require('@/assets/menu_icon/<EMAIL>'),
      iconActive: require('@/assets/menu_icon/<EMAIL>'),
      iconImage: require('@/assets/menu_icon/<EMAIL>'),
      code: 'KJYSBGL',
    },
    children: [
      {
        path: 'deviceManage',
        name: 'deviceManage',
        component: () => import('@/views/aobo/deviceManage/index'),
        meta: {
          title: '空间与设备管理',
          iconDefault: require('@/assets/menu_icon/<EMAIL>'),
          iconActive: require('@/assets/menu_icon/<EMAIL>'),
          iconImage: require('@/assets/menu_icon/<EMAIL>'),
          code: 'KJYSBGL',
        },
      },
    ],
  },
  // {
  //   path: '/currentSensor',
  //   component: Layout,
  //   redirect: '/aobo/currentSensor',
  //   meta: {
  //     iconDefault: require('@/assets/menu_icon/icon_dianliu.png'),
  //     iconActive: require('@/assets/menu_icon/icon_dianliu_xz.png'),
  //     iconImage: require('@/assets/menu_icon/icon_dianliu.png'),
  //     code: 'BJXX',
  //   },
  //   children: [
  //     {
  //       path: 'currentSensor',
  //       name: 'currentSensor',
  //       component: () => import('@/views/aobo/currentSensor/index'),
  //       meta: {
  //         title: '电流传感器管理',
  //         iconDefault: require('@/assets/menu_icon/icon_dianliu.png'),
  //         iconActive: require('@/assets/menu_icon/icon_dianliu_xz.png'),
  //         iconImage: require('@/assets/menu_icon/icon_dianliu.png'),
  //         code: 'KJYSBGL',
  //       },
  //     },
  //   ],
  // },
  {
    path: '/systemManage',
    component: Layout,
    redirect: '/aobo/systemManage',
    meta: {
      title: '系统管理',
      icon: '',
      iconDefault: require('@/assets/menu_icon/<EMAIL>'),
      iconActive: require('@/assets/menu_icon/<EMAIL>'),
      iconImage: require('@/assets/menu_icon/<EMAIL>'),
      code: 'XTGL',
    },
    children: [
      {
        path: 'userManage',
        name: 'userManage',
        component: () => import('@/views/aobo/userManage/index'),
        meta: { title: '用户管理', icon: '', code: 'XTGL-YHGL' },
      },
      {
        path: 'authManage',
        name: 'authManage',
        component: () => import('@/views/aobo/authManage/index'),
        meta: { title: '权限管理', icon: '', code: 'XTGL-QXGL' },
      },
      {
        path: 'alarmTactics',
        name: 'alarmTactics',
        component: () => import('@/views/aobo/alarmTactics/index'),
        meta: { title: '告警策略', icon: '', code: 'XTGL-GJCL' },
      },
      {
        path: 'log',
        name: 'log',
        component: () => import('@/views/aobo/log/index'),
        meta: { title: '系统日志', icon: '', code: 'XTGL-XTRZ' },
      },
      {
        path: 'messageCenter',
        name: 'messageCenter',
        component: () => import('@/views/aobo/messageCenter/index'),
        meta: { title: '消息中心', icon: '', code: 'XTGL-XXZX' },
      },
      {
        path: 'systemSetting',
        name: 'SystemSetting',
        component: () => import('@/views/aobo/systemSetting/index'),
        meta: { title: '页面定制', icon: '', code: 'XTGL-YMDZ' },
      },
    ],
  },
]

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
