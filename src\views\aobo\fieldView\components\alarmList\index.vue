<template>
  <div v-show="isAlarm">
    <div v-if="!showAlarm" class="alarm_icon" @click="showAlarm = true">
      <img src="@/assets/page/alarm.png" />
      <div class="wave-container-alarm">
        <div class="wave wave1" />
        <div class="wave wave2" />
        <div class="wave wave3" />
      </div>
    </div>

    <div v-else class="right" :style="{ marginTop: isFullscreen ? '80px' : '0' }">
      <div class="alarm_list">
        <img class="close_icon" src="@/assets/page/<EMAIL>" @click="showAlarm = false" />
        <div class="title">
          <img src="@/assets/page/alarm.png" />
          <div>实时告警列表</div>
        </div>

        <div class="table_list">
          <div class="wrapper">
            <header class="header"></header>
            <div class="container">
              <div v-for="item in alarmList" :key="item.id" class="list_item" @click="detail(item)">
                <div class="list_item_title">
                  <div class="name">
                    <span>{{ item.segmentName }}</span>
                  </div>
                  <div :style="{ color: alarmColorMap[item.level] }">{{ `${item.tempVal / 100}℃` }}</div>
                </div>
                <div class="list_item_detail">
                  <div style="font-size: 13px; color: #c4ddff">位置：{{ item.cableName }}-{{ item.position / 100 }}m处</div>
                </div>

                <div class="list_item_bottom">
                  <div class="time">
                    <img src="@/assets/page/<EMAIL>" />
                    <div>{{ item.alarmTime }}</div>
                  </div>
                  <img src="@/assets/page/dingwei.png" />
                </div>
              </div>

              <div v-if="!isAlarm" style="display: grid; place-items: center; height: 100%; color: white">暂无数据</div>
            </div>
            <div v-if="isAlarm" style="display: flex;justify-content: flex-end;margin-top: 10px;">
              <span style="color: white">告警总数：</span>
              <span style="color: #ff443a; font-weight: bold; margin-right: 10px">{{ alarmList.length }}条</span>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { alarmColorMap, alarmLevelMap } from '@/constants'

export default {
  name: 'AlarmBattery',
  components: { },
  props: {
    isFullscreen: {
      type: Boolean,
      default: false,
    },
    alarmList: {
      type: Array,
      default() { return [] },
    }
  },
  data() {
    return {
      showAlarm: true,
      hasTempAlarm: false,
      tempAlarmCount: 0,
    }
  },
  computed: {
    alarmLevelMap() {
      return alarmLevelMap
    },
    alarmColorMap() {
      return alarmColorMap
    },
    isAlarm() {
      return this.alarmList?.length
    },
  },
  methods: {
    detail(row) {
      this.$emit('detail', row)
    }

  },
}
</script>

<style scoped lang="scss">
.right {
  position: absolute;
  right: 30px;
  top: 20px;
  bottom: 20px;
  width: 360px;
  z-index: 998 !important;
  .alarm_list {
    width: 100%;
    height: 100%;
    background: url(~@/assets/page/kuang6.png) no-repeat;
    background-size: 100% 100%;
    .close_icon {
      width: 15px;
      height: 15px;
      position: absolute;
      right: 25px;
      top: 13px;
      cursor: pointer;
    }
    .title {
      width: 100%;
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 25px;
        height: 25px;
        margin-right: 7px;
      }
      div {
        font-size: 20px;
        font-weight: bold;
        background: linear-gradient(0deg, #ff443a 0%, #ffdb11 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
    .table_list {
      padding: 0 20px 10px;
      height: 100%;
      overflow: hidden;
      .list_item {
        padding: 0 15px;
        width: 320px;
        height: 155px;
        background: rgba(27, 95, 225, 0.3);
        border-radius: 8px;
        margin-bottom: 15px;
        // font-weight: bold;
        cursor: pointer;
        .list_item_title {
          height: 40px;
          line-height: 40px;
          margin-bottom: 5px;
          border-bottom: 1px solid rgba(142, 216, 246, 0.3);
          display: flex;
          justify-content: space-between;
          font-size: 15px;
          font-weight: bold;
          .name {
            color: #fff;
          }
          .temperature {
            color: #ff511c;
          }
        }
        .list_item_detail {
          font-size: 13px;
          color: #c4ddff;
          line-height: 30px;
          margin-bottom: 5px;
          div {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .list_item_bottom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .time {
            display: flex;
            align-items: center;
            img {
              width: 15px;
              height: 15px;
              margin-right: 10px;
            }
            div {
              color: #91a0b4;
              font-size: 14px;
              font-weight: bold;
              display: flex;
            }
          }
          img {
            width: 20px;
            height: 28px;
            cursor: pointer;
          }
        }
      }
    }
  }
}

.alarm_icon {
  position: absolute;
  right: 90px;
  top: 50px;
  cursor: pointer;
  z-index: 1000;
  img {
    position: absolute;
    width: 50px;
    height: 50px;
    z-index: 2;
  }
}

.wave-container-alarm {
  position: absolute;
  top: -39px;
  left: -35px;
  width: 120px;
  height: 120px;
  z-index: 1;
  .wave {
    position: absolute;
    bottom: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    animation: wave-alarm-animation 3s linear infinite;
    background: red;
  }
  .wave1 {
    animation-delay: -1s;
  }
  .wave2 {
    animation-delay: -2s;
  }
  .wave3 {
    animation-delay: -3s;
  }
}
@keyframes wave-alarm-animation {
  100% {
    width: 120px;
    height: 80px;
    opacity: 0;
    bottom: 0;
    left: 0;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  .left {
    display: flex;
    align-items: center;
    user-select: none;
    white-space: nowrap;
  }
  .name {
    color: #0069f9;
  }
  .room {
    width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .text {
    color: #81b6ff;
  }
  img {
    width: 19px;
    height: 19px;
    cursor: pointer;
    margin-right: 10px;
  }
}
.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.container {
  height: calc(100% - 100px);
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
}
.list_item {
  padding: 0 15px;
  width: 320px;
  height: 155px;
  background: rgba(27, 95, 225, 0.3);
  border-radius: 8px;
  margin-bottom: 15px;
  // font-weight: bold;
  cursor: pointer;
  .list_item_title {
    height: 40px;
    line-height: 40px;
    margin-bottom: 5px;
    border-bottom: 1px solid rgba(142, 216, 246, 0.3);
    display: flex;
    justify-content: space-between;
    font-size: 15px;
    font-weight: bold;
    .name {
      color: #fff;
    }
    .temperature {
      color: #ff511c;
    }
  }
  .list_item_bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .time {
      display: flex;
      align-items: center;
      img {
        width: 15px;
        height: 15px;
        margin-right: 10px;
      }
      div {
        color: #91a0b4;
        font-size: 14px;
        font-weight: bold;
        display: flex;
      }
    }
    img {
      width: 20px;
      height: 28px;
      cursor: pointer;
    }
  }
}

</style>
